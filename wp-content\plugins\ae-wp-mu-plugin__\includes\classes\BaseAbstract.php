<?php
/**
 * American Eagle WP MU Plugin Base
 *
 * @package AmericaneagleWpMUPlugin
 */

namespace AmericaneagleWpMUPlugin;

use AmericaneagleWpMUPlugin\Helper\ContainerTrait;

/**
 * American Eagle WP MU Plugin Base
 *
 * @package AmericaneagleWpMUPlugin
 */
abstract class BaseAbstract {

	use ContainerTrait;

	/**
	 * Array of AE plugins.
	 */
	const AE_PLUGINS = [
		'Steam'                   => 'stream/stream.php',
		'Wordfence'               => 'wordfence/wordfence.php',
		'WP Remote'               => 'wpremote/plugin.php',
		'SAML 2.0 Single Sign-On' => 'miniorange-saml-20-single-sign-on/login.php',
		'File Manager Advanced'   => 'mu-ae-managed-hosting.php/file_manager_advanced.php',
		'WordPress Simple SAML'   => 'wp-simple-saml/plugin.php',
	];

	/**
	 * Array of blacklisted plugins.
	 */
	const BLACKLISTED_PLUGINS = [
		'WP File/ Folder Manager'                    => 'wp-file-manager/file_folder_manager.php',
		'WP File Manager'                            => 'wp-file-manager/wp-file-manager.php',
		'Advanced File Manager'                      => 'file-manager-advanced/',
		'Adminer'                                    => 'adminer/adminer.php',
		'Asynchronous Google Analytics'              => 'async-google-analytics/async-google-analytics.php',
		'JetBackup'                                  => 'backup/backup.php',
		'Backup Scheduler'                           => 'backup-scheduler/backup-scheduler.php',
		'BackupUpWordPress'                          => 'backupwordpress/backupwordpress.php',
		'BackWPup'                                   => 'backwpup/backwpup.php',
		'Bad Behavior'                               => 'bad-behavior/bad-behavior.php',
		'Broken Link Checker'                        => 'broken-link-checker/broken-link-checker.php',
		'Content Molecules'                          => 'content-molecules/emc2_content_molecules.php',
		'Contextual Related Posts'                   => 'contextual-related-posts/contextual-related-posts.php',
		'Duplicator'                                 => 'duplicator/duplicator.php',
		'Dynamic Related Posts'                      => 'dynamic-related-posts/dynamic-related-posts.php',
		'EZPZ One Click Backup'                      => 'ezpz-one-click-backup/ezpz-one-click-backup.php',
		'File Commander'                             => 'file-commander/file-commander.php',
		'Fuzzy SEO Booster'                          => 'fuzzy-seo-booster/fuzzy-seo-booster.php',
		'GD Rating System'                           => 'gd-rating-system/gd-rating-system.php',
		'GD Rating System PHP'                       => 'gd-system-plugin.php/gd-system-plugin.php.php',
		'Google XML Sitemaps with Multisite support' => 'google-xml-sitemaps-with-multisite-support/google-xml-sitemaps-with-multisite-support.php',
		'HC Custom WP-Admin URL'                     => 'hc-custom-wp-admin-url/hc-custom-wp-admin-url.php',
		'HCS'                                        => 'hcs.php/hcs.php.php',
		'Hello'                                      => 'hello.php/hello.php.php',
		'Hyper Cache'                                => 'hyper-cache/plugin.php',
		'JS Referre'                                 => 'jr-referrer/jr-referrer.php',
		'Jumpple'                                    => 'jumpple/jumpple.php',
		'Missed Schedule'                            => 'missed-schedule/missed-schedule.php',
		'No Revisions'                               => 'no-revisions/no-revisions.php',
		'Ozh Who Sees Ads'                           => 'ozh-who-sees-ads/wp_ozh_whoseesads.php',
		'Pipdig Power Pack'                          => 'pipdig-power-pack/pipdig-power-pack.php',
		'Portable Phpmyadmin'                        => 'portable-phpmyadmin/portable-phpmyadmin.php',
		'Quick Cache'                                => 'quick-cache/quick-cache.php',
		'Quick Cache PRO'                            => 'quick-cache-pro/quick-cache-pro.php',
		'Refer A Friend for WooCommerce'             => 'recommend-a-friend/recommend-a-friend.php',
		'SEO Auto Links & Related Posts'             => 'seo-alrp/seo-alrp.php',
		'SI CAPTCHA Anti-Spam'                       => 'si-captcha-for-wordpress/si-captcha-for-wordpress.php',
		'Similar Posts'                              => 'similar-posts/similar-posts.php',
		'SpamReferrerBlock'                          => 'spamreferrerblock/spamreferrerblock.php',
		'Ssclassic'                                  => 'ssclassic/ssclassic.php',
		'Sspro'                                      => 'sspro/sspro.php',
		'Super Post'                                 => 'super-post/super-post.php',
		'SuperSlider'                                => 'superslider/superslider.php',
		'Sweet Captcha'                              => 'sweetcaptcha-revolutionary-free-captcha-service/sweetcaptcha-revolutionary-free-captcha-service.php',
		'Text Passwords'                             => 'text-passwords/text-passwords.php',
		'CodeTree Backup'                            => 'the-codetree-backup/the-codetree-backup.php',
		'Tools Pack'                                 => 'toolspack/toolspack.php',
		'Tools Pack 2'                               => 'ToolsPack/ToolsPack.php',
		'Tweet Blender'                              => 'tweet-blender/tweet-blender.php',
		'VersionPress'                               => 'versionpress/versionpress.php',
		'W3 Total Cache'                             => 'w3-total-cache/w3-total-cache.php',
		'WordPress Gzip Compression'                 => 'wordpress-gzip-compression/wordpress-gzip-compression.php',
		'Super Cache'                                => 'wp-cache/wp-cache.php',
		'WP Database Optimizer'                      => 'wp-database-optimizer/wp-database-optimizer.php',
		'WP DB Backup'                               => 'wp-db-backup/wp-db-backup.php',
		'WP DB Manager'                              => 'wp-dbmanager/wp-dbmanager.php',
		'WP Engine Snapshot'                         => 'wp-engine-snapshot/wp-engine-snapshot.php',
		'WP File Cache'                              => 'wp-file-cache/wp-file-cache.php',
		'WP phpMyAdmin'                              => 'wp-phpmyadmin-extension/index.php',
		'Wp Post Views'                              => 'wp-postviews/wp-postviews.php',
		'Slimstat Analytics'                         => 'wp-slimstat/wp-slimstat.php',
		'WP Super Cache'                             => 'wp-super-cache/wp-cache.php',
		'WP Symposium'                               => 'wp-symposium-alerts/wp-symposium-alerts.php',
		'WP Engine Site Migration'                   => 'wpengine-migrate/wpengine-migrate.php',
		'WP Engine Site Migration Tar'               => 'wpengine-migrate.tar.gz/wpengine-migrate.tar.gz.php',
		'WP Engine Site Migration Zip'               => 'wpengine-migrate.zip/wpengine-migrate.zip.php',
		'WP Engine Snapshot'                         => 'wpengine-snapshot/wpengine-snapshot.php',
		'WP Engine Snapshot Tar'                     => 'wpengine-snapshot.tar.gz/wpengine-snapshot.tar.gz.php',
		'WP Online Backup'                           => 'wponlinebackup/wponlinebackup.php',
	];

	/**
	 * Either inject an already constructed container or create a new on when object is instantiated
	 */
	public function __construct() {
		$this->set_container();
	}
}
