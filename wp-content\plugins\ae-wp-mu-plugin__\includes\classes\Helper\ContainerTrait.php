<?php
/**
 * Core Plugin Container Helper Trait
 *
 * @package AmericaneagleWpMUPlugin\Helper
 */

namespace AmericaneagleWpMUPlugin\Helper;

use League\Container\Container;
use League\Container\ReflectionContainer;

/**
 * Core Plugin Container Helper Trait
 *
 * @package AmericaneagleWpMUPlugin\Helper
 */
trait ContainerTrait {

	/**
	 * App container object.
	 *
	 * @var Container $app
	 */
	protected Container $app;


	/**
	 * Create  the Container object.
	 *
	 * @return void
	 */
	protected function set_container(): void {
		$this->app = new Container();
		$this->app->delegate(
			new ReflectionContainer( true )
		);
	}
}
