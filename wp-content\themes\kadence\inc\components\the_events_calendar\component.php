<?php
/**
 * Kadence\The_Events_Calendar\Component class
 *
 * @package kadence
 */

namespace Kadence\The_Events_Calendar;

use Kadence\Component_Interface;
use Kadence\Kadence_CSS;
use Kadence_Blocks_Frontend;
use function Kadence\kadence;
use function add_action;
use function have_posts;
use function the_post;
use function apply_filters;
use function get_template_part;
use function tribe_get_events_link;
use function tribe_get_event_label_plural;


/**
 * Class for adding The_Events_Calendar plugin support.
 */
class Component implements Component_Interface {

	/**
	 * Associative array of Google Fonts to load.
	 *
	 * Do not access this property directly, instead use the `get_google_fonts()` method.
	 *
	 * @var array
	 */
	protected static $google_fonts = array();

	/**
	 * Gets the unique identifier for the theme component.
	 *
	 * @return string Component slug.
	 */
	public function get_slug() : string {
		return 'the_events_calendar';
	}

	/**
	 * Adds the action and filter hooks to integrate with WordPress.
	 */
	public function initialize() {
		add_action( 'kadence_tribe_events_before_main_tag', array( $this, 'tribe_wapper_before' ) );
		add_action( 'kadence_tribe_events_after_main_tag', array( $this, 'tribe_wapper_after' ) );
		add_action( 'kadence_tribe_archive_events_before_template', array( $this, 'tribe_archive_wapper_before' ), 5 );
		add_action( 'kadence_tribe_archive_events_after_template', array( $this, 'tribe_archive_wapper_after' ) );
		add_action( 'kadence_tribe_events_header', array( $this, 'tribe_event_title_area' ), 10, 2 );
		add_filter( 'kadence_dynamic_css', array( $this, 'dynamic_css' ), 20 );
		add_action( 'wp_head', array( $this, 'frontend_gfonts' ), 80 );
		add_action( 'wp_enqueue_scripts', array( $this, 'tribe_styles' ), 60 );
		add_filter( 'kadence_theme_options_defaults', array( $this, 'add_option_defaults' ) );
		add_filter( 'tribe_default_events_block_single_classes', array( $this, 'events_template_classes' ) );
	}
	/**
	 * Add event template classes.
	 *
	 * @param array $classes template classes.
	 * @return array
	 */
	public function events_template_classes( $classes ) {
		$classes[] = 'entry';
		$classes[] = 'content-bg';
		return $classes;
	}
	/**
	 * Generates the dynamic css based on customizer options.
	 *
	 * @param string $css any custom css.
	 * @return string
	 */
	public function dynamic_css( $css ) {
		$generated_css = $this->generate_events_css();
		if ( ! empty( $generated_css ) ) {
			$css .= "\n/* Kadence Events CSS */\n" . $generated_css;
		}
		return $css;
	}
	/**
	 * Generates the dynamic css based on page options.
	 *
	 * @return string
	 */
	public function generate_events_css() {
		$css                    = new Kadence_CSS();
		$media_query            = array();
		$media_query['mobile']  = apply_filters( 'kadence_mobile_media_query', '(max-width: 767px)' );
		$media_query['tablet']  = apply_filters( 'kadence_tablet_media_query', '(max-width: 1024px)' );
		$media_query['desktop'] = apply_filters( 'kadence_desktop_media_query', '(min-width: 1025px)' );
		$css->set_selector( ':root' );
		$css->add_property( '--tec-color-background-events', 'transparent' );
		$css->add_property( '--tec-color-text-event-date', 'var(--global-palette3)' );
		$css->add_property( '--tec-color-text-event-title', 'var(--global-palette3)' );
		$css->add_property( '--tec-color-text-events-title', 'var(--global-palette3)' );
		$css->add_property( '--tec-color-background-view-selector-list-item-hover', 'var(--global-palette7)' );
		$css->add_property( '--tec-color-background-secondary', 'var(--global-palette8)' );
		$css->add_property( '--tec-color-link-primary', 'var(--global-palette3)' );
		$css->add_property( '--tec-color-icon-active', 'var(--global-palette3)' );
		$css->add_property( '--tec-color-day-marker-month', 'var(--global-palette4)' );
		$css->add_property( '--tec-color-border-active-month-grid-hover', 'var(--global-palette5)' );
		$css->add_property( '--tec-color-accent-primary', 'var(--global-palette1)' );
		$css->add_property( '--tec-color-border-default', 'var(--global-gray-400)' );
		// Events Hero Title Area.
		$css->set_selector( '.tribe_events-hero-section .entry-hero-container-inner' );
		$css->render_background( kadence()->sub_option( 'tribe_events_title_background', 'desktop' ), $css );
		$css->add_property( 'border-top', $css->render_border( kadence()->sub_option( 'tribe_events_title_top_border', 'desktop' ) ) );
		$css->add_property( 'border-bottom', $css->render_border( kadence()->sub_option( 'tribe_events_title_bottom_border', 'desktop' ) ) );
		$css->set_selector( '.entry-hero.tribe_events-hero-section .entry-header' );
		$css->add_property( 'min-height', $css->render_range( kadence()->option( 'tribe_events_title_height' ), 'desktop' ) );
		$css->set_selector( '.tribe_events-hero-section .hero-section-overlay' );
		$css->add_property( 'background', $css->render_color_or_gradient( kadence()->sub_option( 'tribe_events_title_overlay_color', 'color' ) ) );
		$css->start_media_query( $media_query['tablet'] );
		$css->set_selector( '.tribe_events-hero-section .entry-hero-container-inner' );
		$css->render_background( kadence()->sub_option( 'tribe_events_title_background', 'tablet' ), $css );
		$css->add_property( 'border-top', $css->render_border( kadence()->sub_option( 'tribe_events_title_top_border', 'tablet' ) ) );
		$css->add_property( 'border-bottom', $css->render_border( kadence()->sub_option( 'tribe_events_title_bottom_border', 'tablet' ) ) );
		$css->set_selector( '.entry-hero.tribe_events-hero-section .entry-header' );
		$css->add_property( 'min-height', $css->render_range( kadence()->option( 'tribe_events_title_height' ), 'tablet' ) );
		$css->stop_media_query();
		$css->start_media_query( $media_query['mobile'] );
		$css->set_selector( '.tribe_events-hero-section .entry-hero-container-inner' );
		$css->render_background( kadence()->sub_option( 'tribe_events_title_background', 'mobile' ), $css );
		$css->add_property( 'border-top', $css->render_border( kadence()->sub_option( 'tribe_events_title_top_border', 'mobile' ) ) );
		$css->add_property( 'border-bottom', $css->render_border( kadence()->sub_option( 'tribe_events_title_bottom_border', 'mobile' ) ) );
		$css->set_selector( '.entry-hero.tribe_events-hero-section .entry-header' );
		$css->add_property( 'min-height', $css->render_range( kadence()->option( 'tribe_events_title_height' ), 'mobile' ) );
		$css->stop_media_query();
		// Events Title Font.
		$css->set_selector( '.single-tribe_events #inner-wrap .tribe_events-title h1' );
		$css->render_font( kadence()->option( 'tribe_events_title_font' ), $css, 'heading' );
		$css->start_media_query( $media_query['tablet'] );
		$css->set_selector( '.single-tribe_events #inner-wrap .tribe_events-title h1' );
		$css->add_property( 'font-size', $css->render_font_size( kadence()->option( 'tribe_events_title_font' ), 'tablet' ) );
		$css->add_property( 'line-height', $css->render_font_height( kadence()->option( 'tribe_events_title_font' ), 'tablet' ) );
		$css->add_property( 'letter-spacing', $css->render_font_spacing( kadence()->option( 'tribe_events_title_font' ), 'tablet' ) );
		$css->stop_media_query();
		$css->start_media_query( $media_query['mobile'] );
		$css->set_selector( '.single-tribe_events #inner-wrap .tribe_events-title h1' );
		$css->add_property( 'font-size', $css->render_font_size( kadence()->option( 'tribe_events_title_font' ), 'mobile' ) );
		$css->add_property( 'line-height', $css->render_font_height( kadence()->option( 'tribe_events_title_font' ), 'mobile' ) );
		$css->add_property( 'letter-spacing', $css->render_font_spacing( kadence()->option( 'tribe_events_title_font' ), 'mobile' ) );
		$css->stop_media_query();
		// Events Title Breadcrumbs.
		$css->set_selector( '.tribe_events-title .kadence-breadcrumbs' );
		$css->render_font( kadence()->option( 'tribe_events_title_breadcrumb_font' ), $css );
		$css->add_property( 'color', $css->render_color( kadence()->sub_option( 'tribe_events_title_breadcrumb_color', 'color' ) ) );
		$css->set_selector( '.tribe_events-title .kadence-breadcrumbs a:hover' );
		$css->add_property( 'color', $css->render_color( kadence()->sub_option( 'tribe_events_title_breadcrumb_color', 'hover' ) ) );
		$css->start_media_query( $media_query['tablet'] );
		$css->set_selector( '.tribe_events-title .kadence-breadcrumbs' );
		$css->add_property( 'font-size', $css->render_font_size( kadence()->option( 'tribe_events_title_breadcrumb_font' ), 'tablet' ) );
		$css->add_property( 'line-height', $css->render_font_height( kadence()->option( 'tribe_events_title_breadcrumb_font' ), 'tablet' ) );
		$css->add_property( 'letter-spacing', $css->render_font_spacing( kadence()->option( 'tribe_events_title_breadcrumb_font' ), 'tablet' ) );
		$css->stop_media_query();
		$css->start_media_query( $media_query['mobile'] );
		$css->set_selector( '.tribe_events-title .kadence-breadcrumbs' );
		$css->add_property( 'font-size', $css->render_font_size( kadence()->option( 'tribe_events_title_breadcrumb_font' ), 'mobile' ) );
		$css->add_property( 'line-height', $css->render_font_height( kadence()->option( 'tribe_events_title_breadcrumb_font' ), 'mobile' ) );
		$css->add_property( 'letter-spacing', $css->render_font_spacing( kadence()->option( 'tribe_events_title_breadcrumb_font' ), 'mobile' ) );
		$css->stop_media_query();
		// Events Title Back Link.
		$css->set_selector( '.tribe_events-title .tribe-events-back a' );
		$css->render_font( kadence()->option( 'tribe_events_title_back_link_font' ), $css );
		$css->add_property( 'color', $css->render_color( kadence()->sub_option( 'tribe_events_title_back_link_color', 'color' ) ) );
		$css->set_selector( '.tribe_events-title .tribe-events-back a:hover' );
		$css->add_property( 'color', $css->render_color( kadence()->sub_option( 'tribe_events_title_back_link_color', 'hover' ) ) );
		$css->start_media_query( $media_query['tablet'] );
		$css->set_selector( '.tribe_events-title .tribe-events-back a' );
		$css->add_property( 'font-size', $css->render_font_size( kadence()->option( 'tribe_events_title_back_link_font' ), 'tablet' ) );
		$css->add_property( 'line-height', $css->render_font_height( kadence()->option( 'tribe_events_title_back_link_font' ), 'tablet' ) );
		$css->add_property( 'letter-spacing', $css->render_font_spacing( kadence()->option( 'tribe_events_title_back_link_font' ), 'tablet' ) );
		$css->stop_media_query();
		$css->start_media_query( $media_query['mobile'] );
		$css->set_selector( '.tribe_events-title .tribe-events-back a' );
		$css->add_property( 'font-size', $css->render_font_size( kadence()->option( 'tribe_events_title_back_link_font' ), 'mobile' ) );
		$css->add_property( 'line-height', $css->render_font_height( kadence()->option( 'tribe_events_title_back_link_font' ), 'mobile' ) );
		$css->add_property( 'letter-spacing', $css->render_font_spacing( kadence()->option( 'tribe_events_title_back_link_font' ), 'mobile' ) );
		$css->stop_media_query();
		// Single Event Backgrounds.
		$css->set_selector( 'body.single-tribe_events' );
		$css->render_background( kadence()->sub_option( 'tribe_events_background', 'desktop' ), $css );
		$css->set_selector( 'body.single-tribe_events .content-bg, body.content-style-unboxed.single-tribe_events .site' );
		$css->render_background( kadence()->sub_option( 'tribe_events_content_background', 'desktop' ), $css );
		$css->start_media_query( $media_query['tablet'] );
		$css->set_selector( 'body.single-tribe_events' );
		$css->render_background( kadence()->sub_option( 'tribe_events_background', 'tablet' ), $css );
		$css->set_selector( 'body.single-tribe_events .content-bg, body.content-style-unboxed.single-tribe_events .site' );
		$css->render_background( kadence()->sub_option( 'tribe_events_content_background', 'tablet' ), $css );
		$css->stop_media_query();
		$css->start_media_query( $media_query['mobile'] );
		$css->set_selector( 'body.single-tribe_events' );
		$css->render_background( kadence()->sub_option( 'tribe_events_background', 'mobile' ), $css );
		$css->set_selector( 'body.single-tribe_events .content-bg, body.content-style-unboxed.single-tribe_events .site' );
		$css->render_background( kadence()->sub_option( 'tribe_events_content_background', 'mobile' ), $css );
		$css->stop_media_query();
		// Events Backgrounds.
		$css->set_selector( 'body.post-type-archive-tribe_events .site, body.post-type-archive-tribe_events.content-style-unboxed .site' );
		$css->render_background( kadence()->sub_option( 'tribe_events_archive_content_background', 'desktop' ), $css );
		$css->start_media_query( $media_query['tablet'] );
		$css->set_selector( 'body.post-type-archive-tribe_events .site, body.post-type-archive-tribe_events.content-style-unboxed .site' );
		$css->render_background( kadence()->sub_option( 'tribe_events_archive_content_background', 'tablet' ), $css );
		$css->stop_media_query();
		$css->start_media_query( $media_query['mobile'] );
		$css->set_selector( 'body.post-type-archive-tribe_events .site, body.post-type-archive-tribe_events.content-style-unboxed .site' );
		$css->render_background( kadence()->sub_option( 'tribe_events_archive_content_background', 'mobile' ), $css );
		$css->stop_media_query();

		$new_styles   = [];
		$css->set_selector( '#primary .tribe-events, #primary .tribe-events-single' );
		// Events Bar.
		if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'events_bar_text_color' ) ) {
			$text_color = $this->get_tribe_option( 'tec_events_bar', 'events_bar_text_color' );
			// Text color.
			if ( ! empty( $text_color ) ) {
				$css->add_property( '--tec-color-text-events-bar-input', $text_color);
				$css->add_property( '--tec-color-text-events-bar-input-placeholder', $text_color);
				$css->add_property( '--tec-color-text-view-selector-list-item', $text_color);
				$css->add_property( '--tec-color-text-view-selector-list-item-hover', $text_color);
			}
		}

		if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'find_events_button_text_color' ) ) {
			$button_text_color     = $this->get_tribe_option( 'tec_events_bar', 'find_events_button_text_color' );
			if ( ! empty( $button_text_color ) ) {
				$css->add_property( '--tec-color-text-events-bar-submit-button', $button_text_color);
			}
		}


		if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'events_bar_icon_color_choice' ) ) {
			if ( 'custom' === $this->get_tribe_option( 'tec_events_bar', 'events_bar_icon_color_choice' ) ) {
				$icon_color = $this->get_tribe_option( 'tec_events_bar', 'events_bar_icon_color' );
			} elseif (
				'accent' === $this->get_tribe_option( 'tec_events_bar', 'events_bar_icon_color_choice' )
				&& $this->should_include_tribe_setting_css( 'global_elements', 'accent_color' )
			) {
				$icon_color = $this->get_tribe_option( 'global_elements', 'accent_color' );
			}

			if ( ! empty( $icon_color ) ) {
				$css->add_property( '--tec-color-icon-events-bar', $icon_color);
				$css->add_property( '--tec-color-icon-events-bar-hover', $icon_color);
				$css->add_property( '--tec-color-icon-events-bar-active', $icon_color);
			}
		}

		if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'find_events_button_color_choice' ) ) {
			$button_color     = $this->get_tribe_option( 'tec_events_bar', 'find_events_button_color' );
			$button_color_rgb = $css->hex2rgb( $button_color );
		} elseif ( $this->should_include_tribe_setting_css( 'global_elements', 'accent_color' ) ) {
			$button_color     = $this->get_tribe_option( 'global_elements', 'accent_color' );
			$button_color_rgb = $css->hex2rgb( $button_color );
		}

		if ( ! empty( $button_color ) ) {
			$css->add_property( '--tec-color-background-events-bar-submit-button', $button_color);
			//$new_styles[] = "--tec-color-background-events-bar-submit-button: {$button_color};";
		}

		if ( ! empty( $button_color_rgb ) ) {
			$css->add_property( '--tec-color-background-events-bar-submit-button-hover', 'rgba( ' . $button_color_rgb . ', 0.8)');
			$css->add_property( '--tec-color-background-events-bar-submit-button-active', 'rgba( ' . $button_color_rgb . ', 0.9)');

		}

		if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'events_bar_background_color_choice' ) ) {
			if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'events_bar_background_color' ) ) {
				if ( 'custom' == $this->get_tribe_option( 'tec_events_bar', 'events_bar_background_color_choice' ) ) {
					$background_color = $this->get_tribe_option( 'tec_events_bar', 'events_bar_background_color' );
				} elseif (
					'global_background' == $this->get_tribe_option( 'tec_events_bar', 'events_bar_background_color_choice' )
					&& $this->should_include_tribe_setting_css( 'global_elements', 'background_color' )
				) {
					$background_color = $this->get_tribe_option( 'global_elements', 'background_color' );
				}
			}

			if ( ! empty( $background_color ) ) {
				$css->add_property( '--tec-color-background-events-bar', $background_color);
				$css->add_property( '--tec-color-background-events-bar-tabs', $background_color);
			}
		}

		if ( $this->should_include_tribe_setting_css( 'tec_events_bar', 'events_bar_border_color_choice' ) ) {
			$border_color = $this->get_tribe_option( 'tec_events_bar', 'events_bar_border_color' );
			if ( ! empty( $border_color ) ) {
				$css->add_property( '--tec-color-border-events-bar', $border_color);
			}
		}
		// Event Title overrides.
		if ( $this->should_include_tribe_setting_css( 'global_elements', 'event_title_color' ) ) {
			$title_color  = $this->get_tribe_option( 'global_elements', 'event_title_color' );
			$css->add_property( '--tec-color-text-events-title', $title_color);
			$css->add_property( '--tec-color-text-event-title', $title_color);
		}
		if (
			$this->should_include_tribe_setting_css( 'month_view', 'multiday_event_bar_color_choice' )
			&& $this->should_include_tribe_setting_css( 'month_view', 'multiday_event_bar_color' )
		) {
			$bar_color     = $this->get_tribe_option( 'month_view', 'multiday_event_bar_color' );
			$bar_color_rgb = $css->hex2rgb( $bar_color );
			$css->add_property( '--tec-color-background-primary-multiday', 'rgba( ' . $bar_color_rgb . ', 0.24)');
			$css->add_property( '--tec-color-background-primary-multiday-hover', 'rgba( ' . $bar_color_rgb . ', 0.34)');
			$css->add_property( '--tec-color-background-primary-multiday-active', 'rgba( ' . $bar_color_rgb . ', 0.34)');
			$css->add_property( '--tec-color-background-secondary-multiday', 'rgba( ' . $bar_color_rgb . ', 0.24)');
			$css->add_property( '--tec-color-background-secondary-multiday-hover', 'rgba( ' . $bar_color_rgb . ', 0.34)');
		}
		if ( $this->should_include_tribe_setting_css( 'month_view', 'date_marker_color' )  ) {
			$date_marker_color = $this->get_tribe_option( 'month_view', 'date_marker_color' );
			$css->add_property( '--tec-color-day-marker-month', $date_marker_color);
			$css->add_property( '--tec-color-day-marker-past-month', $date_marker_color);
		}
		if ( $this->should_include_tribe_setting_css( 'month_view', 'days_of_week_color' )  ) {
			$days_of_week_color = $this->get_tribe_option( 'month_view', 'days_of_week_color' );$css->add_property( '--tec-color-text-day-of-week-month', $days_of_week_color);
		}

		self::$google_fonts = $css->fonts_output();
		return $css->css_output();
	}
	/**
	 * Function to simplify getting an option value.
	 *
	 * @since 4.13.3
	 *
	 * @param string $setting The setting slug, like 'grid_lines_color'.
	 *
	 * @return string The setting value;
	 */
	public function get_tribe_option( $section, $setting ) {
		if ( empty( $setting ) || empty( $section ) ) {
			return '';
		}
		if ( ! function_exists( 'tribe' ) ) {
			return '';
		}

		return tribe( 'customizer' )->get_option( array( $section, $setting ) );
	}
	/**
	 * Check if a setting should be included in the CSS output by making sure it's not the default.
	 *
	 * @param string $section_slug The slug for the section.
	 * @param string $setting The setting slug.
	 *
	 * @return boolean If the setting should be added to the style template.
	 */
	public function should_include_tribe_setting_css( $section_slug, $setting ) {
		if ( empty( $setting ) || empty( $section_slug ) ) {
			return false;
		}
		if ( ! function_exists( 'tribe' ) ) {
			return false;
		}
		$setting_value = tribe( 'customizer' )->get_option( array( $section_slug, $setting ) );
		$section       = tribe( 'customizer' )->get_section( $section_slug );
		// Something has gone wrong and we can't get the section.
		if ( false === $section ) {
			return false;
		}
		return ! empty( $setting_value ) && $section->get_default( $setting ) !== $setting_value;
	}
	/**
	 * Outputs the theme wrappers.
	 */
	public function tribe_event_title_area( $area = 'normal', $template_class = null ) {
		$enable = kadence()->option( 'tribe_events_title' );
		if ( ! $enable ) {
			return;
		}
		$placement = kadence()->option( 'tribe_events_title_layout' );
		if ( $area !== $placement ) {
			return;
		}
		if ( 'normal' === $area ) {
			if ( ! kadence()->show_in_content_title() ) {
				return;
			}
			$classes   = array();
			$classes[] = 'entry-header';
			$classes[] = 'tribe_events-title';
			$classes[] = 'title-align-' . ( kadence()->sub_option( 'tribe_events_title_align', 'desktop' ) ? kadence()->sub_option( 'tribe_events_title_align', 'desktop' ) : 'inherit' );
			$classes[] = 'title-tablet-align-' . ( kadence()->sub_option( 'tribe_events_title_align', 'tablet' ) ? kadence()->sub_option( 'tribe_events_title_align', 'tablet' ) : 'inherit' );
			$classes[] = 'title-mobile-align-' . ( kadence()->sub_option( 'tribe_events_title_align', 'mobile' ) ? kadence()->sub_option( 'tribe_events_title_align', 'mobile' ) : 'inherit' );
			?>
			<div class="<?php echo esc_attr( implode( ' ', $classes ) ); ?>">
			<?php
		}
		$elements = kadence()->option( 'tribe_events_title_elements' );
		if ( isset( $elements ) && is_array( $elements ) && ! empty( $elements ) ) {
			foreach ( $elements as $item ) {
				if ( kadence()->sub_option( 'tribe_events_title_element_' . $item, 'enabled' ) ) {
					switch ( $item ) {
						case 'breadcrumb':
							$template = apply_filters( 'kadence_title_elements_template_path', 'template-parts/title/' . $item, $item, $area );
							get_template_part( $template );
							break;
						case 'back_link':
							if ( null !== $template_class & is_object( $template_class ) ) {
								$template_class->template( 'single-event/back-link' );
							} else {
								$template = apply_filters( 'kadence_title_elements_template_path', 'template-parts/title/' . $item, $item, $area );
								get_template_part( $template );
							}
							break;
						case 'title':
							do_action( 'kadence_single_before_entry_title' );
							if ( null !== $template_class & is_object( $template_class ) ) {
								$template_class->template( 'single-event/title' );
							} else {
								the_title( '<h1 class="entry-title tribe-events-single-event-title">', '</h1>' );
							}
							do_action( 'kadence_single_after_entry_title' );
							break;
						default:
							# code...
							break;
					}
				}
			}
		}
		if ( 'normal' === $area ) {
			?>
			</div>
			<?php
		}
	}
	/**
	 * Add Defaults
	 *
	 * @access public
	 * @param array $defaults registered option defaults with kadence theme.
	 * @return array
	 */
	public function add_option_defaults( $defaults ) {
		// event.
		$event_addons = array(
			'tribe_events_title'              => true,
			'tribe_events_title_layout'       => 'normal',
			'tribe_events_title_inner_layout' => 'standard',
			'tribe_events_title_height'       => array(
				'size' => array(
					'mobile'  => '',
					'tablet'  => '',
					'desktop' => '',
				),
				'unit' => array(
					'mobile'  => 'px',
					'tablet'  => 'px',
					'desktop' => 'px',
				),
			),
			'tribe_events_title_font' => array(
				'size' => array(
					'desktop' => '',
				),
				'lineHeight' => array(
					'desktop' => '',
				),
				'family'  => 'inherit',
				'google'  => false,
				'weight'  => '',
				'variant' => '',
				'color'   => '',
			),
			'tribe_events_layout'           => 'narrow',
			'tribe_events_content_style'    => 'boxed',
			'tribe_events_vertical_padding' => 'show',
			'tribe_events_sidebar_id'       => 'sidebar-primary',
			'tribe_events_title_elements'   => array( 'breadcrumb', 'back_link', 'title' ),
			'tribe_events_title_element_title' => array(
				'enabled' => true,
			),
			'tribe_events_title_element_breadcrumb' => array(
				'enabled' => false,
				'show_title' => true,
			),
			'tribe_events_title_element_back_link' => array(
				'enabled' => true,
			),
			'tribe_events_background'         => '',
			'tribe_events_content_background' => '',
			'tribe_events_title_background'   => array(
				'desktop' => array(
					'color' => '',
				),
			),
			'tribe_events_title_featured_image' => false,
			'tribe_events_title_overlay_color'  => array(
				'color' => '',
			),
			'tribe_events_title_top_border'    => array(),
			'tribe_events_title_bottom_border' => array(),
			'tribe_events_title_align'         => array(
				'mobile'  => '',
				'tablet'  => '',
				'desktop' => '',
			),
			'tribe_events_title_breadcrumb_color' => array(
				'color' => '',
				'hover' => '',
			),
			'tribe_events_title_breadcrumb_font'   => array(
				'size' => array(
					'desktop' => '',
				),
				'lineHeight' => array(
					'desktop' => '',
				),
				'family'  => 'inherit',
				'google'  => false,
				'weight'  => '',
				'variant' => '',
			),
			'tribe_events_title_back_link_color' => array(
				'color' => '',
				'hover' => '',
			),
			'tribe_events_title_back_link_font'   => array(
				'size' => array(
					'desktop' => '',
				),
				'lineHeight' => array(
					'desktop' => '',
				),
				'family'  => 'inherit',
				'google'  => false,
				'weight'  => '',
				'variant' => '',
			),
			'tribe_events_archive_title'              => false,
			'tribe_events_archive_title_layout'       => 'normal',
			'tribe_events_archive_layout'             => 'normal',
			'tribe_events_archive_sidebar_id'         => 'sidebar-primary',
			'tribe_events_archive_content_style'      => 'unboxed',
			'tribe_events_archive_vertical_padding'   => 'show',
			'transparent_header_tribe_events_archive' => true,
		);
		$defaults = array_merge(
			$defaults,
			$event_addons
		);
		return $defaults;
	}
	/**
	 * Outputs the theme wrappers.
	 */
	public function tribe_archive_wapper_before() {
		?>
		<div id="primary" class="content-area">
			<div class="content-container site-container">
		<?php
	}
	/**
	 * Outputs the theme wrappers.
	 */
	public function tribe_archive_wapper_after() {
		get_sidebar();
		?>
			</div>
		</div>
		<?php
	}
	/**
	 * Outputs the theme wrappers.
	 */
	public function tribe_wapper_before() {
		/**
		* Hook for Hero Section
		*/
		do_action( 'kadence_hero_header' );
		?>
		<div id="primary" class="content-area">
			<div class="content-container site-container">
		<?php
	}
	/**
	 * Outputs the theme wrappers.
	 */
	public function tribe_wapper_after() {
		get_sidebar();
		?>
			</div>
		</div>
		<?php
	}
	/**
	 * Add some css styles for Tribe Events
	 */
	public function tribe_styles() {
		wp_enqueue_style( 'kadence-tribe', get_theme_file_uri( '/assets/css/tribe-events.min.css' ), array(), KADENCE_VERSION );
	}
	/**
	 * Enqueue Frontend Fonts
	 */
	public function frontend_gfonts() {
		if ( empty( self::$google_fonts ) ) {
			return;
		}
		if ( class_exists( 'Kadence_Blocks_Frontend' ) ) {
			$ktblocks_instance = Kadence_Blocks_Frontend::get_instance();
			foreach ( self::$google_fonts as $key => $font ) {
				if ( ! array_key_exists( $key, $ktblocks_instance::$gfonts ) ) {
					$add_font = array(
						'fontfamily'   => $font['fontfamily'],
						'fontvariants' => ( isset( $font['fontvariants'] ) && ! empty( $font['fontvariants'] ) && is_array( $font['fontvariants'] ) ? $font['fontvariants'] : array() ),
						'fontsubsets'  => ( isset( $font['fontsubsets'] ) && ! empty( $font['fontsubsets'] ) && is_array( $font['fontsubsets'] ) ? $font['fontsubsets'] : array() ),
					);
					$ktblocks_instance::$gfonts[ $key ] = $add_font;
				} else {
					foreach ( $font['fontvariants'] as $variant ) {
						if ( ! in_array( $variant, $ktblocks_instance::$gfonts[ $key ]['fontvariants'], true ) ) {
							array_push( $ktblocks_instance::$gfonts[ $key ]['fontvariants'], $variant );
						}
					}
				}
			}
		} else {
			add_filter( 'kadence_theme_google_fonts_array', array( $this, 'filter_in_fonts' ) );
		}
	}
	/**
	 * Filters in pro fronts for output with free.
	 *
	 * @param array $font_array any custom css.
	 * @return array
	 */
	public function filter_in_fonts( $font_array ) {
		// Enqueue Google Fonts.
		foreach ( self::$google_fonts as $key => $font ) {
			if ( ! array_key_exists( $key, $font_array ) ) {
				$add_font = array(
					'fontfamily'   => $font['fontfamily'],
					'fontvariants' => ( isset( $font['fontvariants'] ) && ! empty( $font['fontvariants'] ) && is_array( $font['fontvariants'] ) ? $font['fontvariants'] : array() ),
					'fontsubsets'  => ( isset( $font['fontsubsets'] ) && ! empty( $font['fontsubsets'] ) && is_array( $font['fontsubsets'] ) ? $font['fontsubsets'] : array() ),
				);
				$font_array[ $key ] = $add_font;
			} else {
				foreach ( $font['fontvariants'] as $variant ) {
					if ( ! in_array( $variant, $font_array[ $key ]['fontvariants'], true ) ) {
						array_push( $font_array[ $key ]['fontvariants'], $variant );
					}
				}
			}
		}
		return $font_array;
	}
}
