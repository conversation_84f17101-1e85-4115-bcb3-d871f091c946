{"key": "post_type_685ebcb407835", "title": "Caregiver Spotlights", "menu_order": 0, "active": true, "post_type": "caregiver-spotlight", "advanced_configuration": false, "import_source": "", "import_date": "", "labels": {"name": "Caregiver Spotlights", "singular_name": "Caregiver Spotlight", "menu_name": "Caregiver Spotlights", "all_items": "All Caregiver Spotlights", "edit_item": "Edit Caregiver Spotlight", "view_item": "View Caregiver Spotlight", "view_items": "View Caregiver Spotlights", "add_new_item": "Add New Caregiver Spotlight", "add_new": "Add New Caregiver Spotlight", "new_item": "New Caregiver Spotlight", "parent_item_colon": "Parent Caregiver Spotlight:", "search_items": "Search Caregiver Spotlights", "not_found": "No caregiver spotlights found", "not_found_in_trash": "No caregiver spotlights found in Trash", "archives": "Caregiver Spotlight Archives", "attributes": "Caregiver Spotlight Attributes", "featured_image": "", "set_featured_image": "", "remove_featured_image": "", "use_featured_image": "", "insert_into_item": "Insert into caregiver spotlight", "uploaded_to_this_item": "Uploaded to this caregiver spotlight", "filter_items_list": "Filter caregiver spotlights list", "filter_by_date": "Filter caregiver spotlights by date", "items_list_navigation": "Caregiver Spotlights list navigation", "items_list": "Caregiver Spotlights list", "item_published": "Caregiver Spotlight published.", "item_published_privately": "Caregiver Spotlight published privately.", "item_reverted_to_draft": "Caregiver Spotlight reverted to draft.", "item_scheduled": "Caregiver Spotlight scheduled.", "item_updated": "Caregiver Spotlight updated.", "item_link": "Caregiver Spotlight Link", "item_link_description": "A link to a caregiver spotlight."}, "description": "", "public": true, "hierarchical": false, "exclude_from_search": false, "publicly_queryable": true, "show_ui": true, "show_in_menu": true, "admin_menu_parent": "", "show_in_admin_bar": true, "show_in_nav_menus": true, "show_in_rest": true, "rest_base": "", "rest_namespace": "wp/v2", "rest_controller_class": "WP_REST_Posts_Controller", "menu_position": "", "menu_icon": {"type": "dashicons", "value": "dashicons-admin-post"}, "rename_capabilities": false, "singular_capability_name": "post", "plural_capability_name": "posts", "supports": ["title", "editor", "thumbnail", "custom-fields"], "taxonomies": "", "has_archive": false, "has_archive_slug": "", "rewrite": {"permalink_rewrite": "post_type_key", "with_front": "1", "feeds": "0", "pages": "1"}, "query_var": "post_type_key", "query_var_name": "", "can_export": true, "delete_with_user": false, "register_meta_box_cb": "", "enter_title_here": "", "modified": 1752245472}