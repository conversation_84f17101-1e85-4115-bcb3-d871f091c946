<?php
/**
 * American Eagle WP MU Plugin login Class
 *
 * @package AmericaneagleWpMUPlugin\Admin
 */

namespace AmericaneagleWpMUPlugin\Admin;

use AmericaneagleWpMUPlugin\BaseAbstract;
use AmericaneagleWpMUPlugin\is_ae_user;

/**
 * WordPress login Functionality.
 */
class Login extends BaseAbstract {

	/**
	 * Customize AE login page.
	 *
	 * @return void
	 */
	public function ae_login_page(): void {
		?>
			<style type="text/css">
				#mo_saml_button > div:nth-child(2) {
					display: none;
				}

				#mo_saml_login_sso_button {
					background:  #fff !important;
					border-color: rgba(255,255,255,0) !important;
				}

				#mo_saml_login_sso_button > img {
					width: 260px !important;
					height: 50px !important;
				}

				#mo_saml_button {
					margin-bottom: 4rem;
				}

				#mo_saml_title {
					font-size: 1rem;
					font-weight: 600;
				}

				#mo_saml_wp_default_title {
					font-size: .8rem;
					font-weight: 600;
					text-align: center;
				}
                
			</style>
			<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
			<script type="text/javascript">
				jQuery(document).ready(function() {
					document.querySelector("#mo_saml_login_sso_button").innerHTML = '<img src="https://ameagle-assets.azureedge.net/aecom-blobs/images/default-source/default-album/logo77462a6763c4412b9e51c1748903cc85.png">"';
					jQuery("#mo_saml_button").append('<p id="mo_saml_title" style="text-align: center;">Employee Login</p><br><p id="mo_saml_wp_default_title">OR</p>');
				});
			</script>
		<?php 
	}
}
