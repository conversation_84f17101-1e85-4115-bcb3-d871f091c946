<?php

/**
 * Enqueue child styles.
 */
 
function child_enqueue_styles() {
	wp_enqueue_style( 'child-theme', get_stylesheet_directory_uri() . '/style.css', array(), null );
}
add_action( 'wp_enqueue_scripts', 'child_enqueue_styles', 15 );

function remove_global_css() {
	remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
	remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
	remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
}
add_action('init', 'remove_global_css');

// Shortcode for dynamic year - use [year] in footer or as needed
function year_shortcode() {
  $year = date('Y');
  return $year;
}
add_shortcode('year', 'year_shortcode');

/* Update default GF error message gform_validation_message_FORMNUMBER */
add_filter( 'gform_validation_message_1', function ( $message, $form ) {
    if ( gf_upgrade()->get_submissions_block() ) {
        return $message;
    }

    $message = "<h2 class='gform_submission_error hide_summary'>Email address is required. Please <NAME_EMAIL> format</h2>";


    return $message;
}, 10, 2 );

/**
 * ACF Local JSON Configuration
 * Save and load ACF field groups from the child theme
 */

// Set custom save path for ACF JSON files
add_filter('acf/settings/save_json', 'nsc_acf_json_save_point');
function nsc_acf_json_save_point( $path ) {
    // Update path to save JSON files in child theme
    $path = get_stylesheet_directory() . '/acf-json';
    return $path;
}

// Set custom load path for ACF JSON files
add_filter('acf/settings/load_json', 'nsc_acf_json_load_point');
function nsc_acf_json_load_point( $paths ) {
    // Remove original path (optional)
    unset($paths[0]);

    // Add new path to load JSON files from child theme
    $paths[] = get_stylesheet_directory() . '/acf-json';

    return $paths;
}

/**
 * Helper function to export all ACF field groups to PHP format
 * This function can be called to generate PHP code for all field groups
 * Usage: Call nsc_export_acf_to_php() in a temporary admin page or via WP-CLI
 */
function nsc_export_acf_to_php() {
    if ( ! function_exists('acf_get_field_groups') ) {
        return 'ACF is not active';
    }

    $field_groups = acf_get_field_groups();
    $output = "<?php\n\n";
    $output .= "/**\n";
    $output .= " * ACF Field Groups for North Shore Care Center\n";
    $output .= " * Generated on " . date('Y-m-d H:i:s') . "\n";
    $output .= " * Add this to functions.php to register field groups via PHP\n";
    $output .= " */\n\n";

    if ( empty($field_groups) ) {
        return $output . "// No field groups found\n";
    }

    $output .= "add_action('acf/include_fields', function() {\n";
    $output .= "\tif ( ! function_exists('acf_add_local_field_group') ) {\n";
    $output .= "\t\treturn;\n";
    $output .= "\t}\n\n";

    foreach ( $field_groups as $field_group ) {
        // Get the full field group with fields
        $full_group = acf_get_field_group($field_group['key']);
        $full_group['fields'] = acf_get_fields($field_group['key']);

        // Prepare for export
        $full_group = acf_prepare_field_group_for_export($full_group);

        $output .= "\t// Field Group: " . $full_group['title'] . "\n";
        $output .= "\tacf_add_local_field_group(" . var_export($full_group, true) . ");\n\n";
    }

    $output .= "});\n";

    return $output;
}

/**
 * Admin function to display ACF export (for development use)
 * Add ?nsc_acf_export=1 to any admin page URL to see the export
 */
add_action('admin_init', function() {
    if ( isset($_GET['nsc_acf_export']) && current_user_can('manage_options') ) {
        echo '<pre style="background: #f1f1f1; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo htmlspecialchars(nsc_export_acf_to_php());
        echo '</pre>';
        exit;
    }
});