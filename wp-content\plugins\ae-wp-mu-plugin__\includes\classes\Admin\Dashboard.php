<?php
/**
 * American Eagle WP MU Plugin dashboard Class
 *
 * @package AmericaneagleWpMUPlugin\Admin
 */

namespace AmericaneagleWpMUPlugin\Admin;

use AmericaneagleWpMUPlugin\BaseAbstract;

/**
 * WordPress dashboard Functionality.
 */
class Dashboard extends BaseAbstract {

	/**
	 * Hides menu items in WP Admin area.
	 *
	 * @return void
	 */
	public function hide_menu_pages(): void {
		if ( is_ae_user() ) {
			return;
		}

		remove_menu_page( 'wp_stream' );
		remove_menu_page( 'Wordfence' );
		remove_menu_page( 'wpremote' );
		remove_menu_page( 'mo_saml_settings' );
		remove_submenu_page( 'themes.php', 'theme-editor.php' );
		remove_submenu_page( 'plugins.php', 'plugin-editor.php' );
	}

	/**
	 * Hides WP widgets.
	 *
	 * @return void
	 */
	public function hide_dashboard_widgets(): void {
		if ( is_ae_user() ) {
			return;
		}

		remove_meta_box( 'wordfence_activity_report_widget', 'dashboard', 'normal' );			
	}
}
