@import "variables.scss";
@import "builder-controls.scss";
@import "icon-controls.scss";
li#accordion-section-kadence_customizer_custom_posts_placeholder {pointer-events: none;margin: 10px 0 0;}
li#accordion-section-kadence_customizer_custom_posts_placeholder > h3.accordion-section-title {border: 0;background: transparent;text-transform: uppercase;font-size: 80%;}
li#accordion-section-kadence_customizer_custom_posts_placeholder > h3.accordion-section-title:after {display: none;}
.kadence-units .components-dropdown__content .components-popover__content>div {
	padding: 5px 0;
	.components-button.components-dropdown-menu__menu-item.has-icon {
		padding: 0;
		min-width: 32px;
	}
}
.wrap-components-custom-gradient-picker:before {
    content: '';
    background-image: linear-gradient(45deg, #ddd 25%, transparent 0),linear-gradient(-45deg, #ddd 25%, transparent 0),linear-gradient(45deg, transparent 75%, #ddd 0),linear-gradient(-45deg, transparent 75%, #ddd 0);
    background-size: 10px 10px;
    background-position: 0 0,0 5px,5px -5px,-5px 0;
    left: 0;
    right: 0;
    height: 100%;
    position: absolute;
    z-index: -1;
    border-radius: 24px;
}
// .components-custom-gradient-picker__gradient-bar.has-gradient:before {
// 	opacity: 0;
// }
.components-custom-gradient-picker__gradient-bar {
    position: relative;
	//z-index: 20;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-button {
    box-shadow: 0px 0px 0px 2px rgba(0,0,0,.8);
}
// Kadence Range CSS
.kadence-range-control-inner {
    box-sizing: border-box;
    align-items: flex-start;
    display: inline-flex;
    -webkit-box-pack: start;
    justify-content: flex-start;
    padding: 0px;
    position: relative;
    width: 100%;
}
.kadence-range-control-inner .components-range-control.kadence-range-control-range {
    flex-grow: 1;
    margin-bottom: 0;
}
.kadence-range-control {
    width: 100%;
}
.kadence-range-control-inner .components-base-control.kt-range-number-input{
margin-left: 16px;
margin-bottom: 0;
flex: 0 1 65px;
}
.kadence-control-field button.components-button.kadence-reset span.dashicon {
    height: auto;
    width: auto;
    font-size: 12px;
}
#customize-theme-controls .customize-pane-child.accordion-section-content {
	padding: 12px 24px;
	.customize-section-title {
		margin: -12px -24px 0 -24px;
	}
}
#customize-theme-controls .customize-pane-child.accordion-section-content.control-section-sidebar {
	padding: 12px;
}
.kadence-units {
	max-width: 60px;
	.components-dropdown-menu {
		padding: 0;
		min-height: 0;
		border-color: #e2e4e7;
	}
	.components-button.components-dropdown-menu__toggle {
		min-height: 28px;
    	min-width: 30px;
		height: auto;
	}
	.components-popover__content {
		min-width: 50px;
		width: 50px;
		button.components-button {
			height: 36px;
			text-align: center;
			justify-content: center;
			box-shadow: none;
			svg {
				margin:0;
			}
		}
	}
}
.kadence-range-control {
	.kadence-responsive-controls-content {
		display: flex;
	}
	.components-range-control {
		flex-grow: 1;
	}
	input.components-range-control__number {
		max-width: 65px;
		margin-left: 18px;
		border: 1px solid #e2e4e7;
		border-radius: 0;
		margin-right: 1px;
	}
	.components-base-control__field {
		margin: 0;
	}
	button.components-button {
		height: 28px;
	}
}
.kadence-control-field.radio-btn-width-50 .kadence-radio-container-control {
    flex-wrap: wrap;
}
.kadence-control-field.radio-btn-width-50 .kadence-radio-container-control button.components-button.is-tertiary {
    min-width: 45%;
    margin: 4px;
}
.kadence-responsive-control-bar {
    display: flex;
    justify-content: space-between;
    position: relative;
	margin-bottom: 10px;
	.floating-controls {
		.components-button.is-tertiary:not( .active-device ) {
			color:$color-gray-500;
		}
		.components-button.is-tertiary:not( .active-device ):hover {
			color:$color-gray-600;
		}
		.components-button {
			height:18px;
			padding-top: 0;
			padding-bottom: 0;
			box-shadow: none;
			svg {
				height:16px;
				width:16px;
			}
			&:focus:not(:disabled) {
				color: $color-primary;
				box-shadow: none;
			}
		}
		.components-button-group {
			display: flex;
			border: 0;
		}
	}
}
.kadence-control-field .components-button-group {
    border: 0;
}
.kadence-control-field {
	position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
	.customize-control-title {
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 0;
		display: flex;
		align-items: center;
		letter-spacing: 0.1px;
		line-height: 18px;
	}
	button.components-button.kadence-reset {
		height: 18px;
		padding: 0 5px;
		margin-right: 0;
		margin-left: -20px;
		opacity: 0.5;
		svg {
			width: 12px;
			height: 12px;
		}
		&:disabled {
			opacity: 0;
		}
		&:not(:disabled ):hover {
			opacity: 1;
			color: $color-primary;
			box-shadow:none !important;
		}
	}
}
.kadence-radio-container-control {
    display: flex;
    padding: 10px;
	background: #f9f9f9;
	border:0;
	flex-wrap: wrap;
	button.components-button.is-tertiary {
		flex: 1 1 0;
		display: flex;
		-webkit-box-align: center;
		align-items: center;
		justify-content: center;
		font-size: 11px;
		font-weight: 600;
		font-style: normal;
		text-transform: uppercase;
		height: 40px;
		line-height: normal;
		margin: 0;
		padding: 4px;
		border: 1px solid $color-gray-400;
		border-radius: 2px;
		background: transparent;
		color: $color-gray-700;
		white-space: normal;
		box-shadow: none;
		&:not(:first-child) {
			margin-left: 4px;
		}
		&:not(:disabled):not([aria-disabled=true]):hover {
			border-color: $color-gray-600;
			color: $color-gray-700;
		}
		&.active-radio {
			border-color: $color-primary;
			background: $color-primary;
			color: #fff;
			&:not(:disabled):not([aria-disabled=true]):hover {
				color: #fff;
				border-color: $color-primary;
			}
		}
		.kadence-radio-icon {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	&.kadence-radio-icon-container-control {
		margin-top: 10px;
		button.components-button.is-tertiary {
			padding: 5px;
			height: 50px;
			svg {
				width:100%;
				height:auto;
				max-height: 100%;
			}
		}
	}
	
}
.kadence-popover-color .components-popover__content {
	min-width: 240px;
	.components-focal-point-picker-wrapper {
		box-sizing: border-box;
	}
	.components-focal-point-picker_position-display-container input[type=number].components-text-control__input {
		min-height: 16px;
		line-height: 16px;
		font-size: 12px;
		width:50px;
		font-weight: normal;
	}
	.components-focal-point-picker_position-display-container .components-base-control {
		flex: 1;
		margin-bottom: 0;
	}
	.components-focal-point-picker_position-display-container .components-base-control .components-base-control__label {
		margin-bottom: 0;
		margin-right: 0.2em;
	}	
	.components-focal-point-picker_position-display-container .components-base-control__field {
		display: flex;
		align-items: center;
		font-size: 8px;
		font-weight: 600;
		font-style: normal;
		text-transform: uppercase;
	}
	.components-focal-point-picker_position-display-container .components-base-control:last-child .components-base-control__field {
		justify-content: flex-end;
	}
	.actions {
		display: flex;
		justify-content: center;
		margin-bottom: 10px;
		.button {
			flex: 1;
			margin-top: 10px;
		}
	}
}
.kadence-background-picker-wrap .kadence-popover-color .components-popover__content {
	min-width: 300px;
    min-height: 340px;
	max-height: 60vh;
	> div {
		min-height: 320px;
	}
}
.components-popover.kadence-popover-add-builder .components-popover__content {
    bottom: 0;
}
.components-button.kadence-color-icon-indicate {
    height: auto;
    position: relative;
    transform: scale(1);
    transition: transform .1s ease;
    border-radius: 50%;
    padding: 0;
    background-image: linear-gradient(45deg, #ddd 25%, transparent 0), linear-gradient(-45deg, #ddd 25%, transparent 0), linear-gradient(45deg, transparent 75%, #ddd 0), linear-gradient(-45deg, transparent 75%, #ddd 0);
    background-size: 10px 10px;
    background-position: 0 0,0 5px,5px -5px,-5px 0;
	.component-color-indicator.kadence-advanced-color-indicate {
		width: 28px;
		height: 28px;
		border-radius: 50%;
		margin: 0;
	}
	&:hover {
		transform: scale(1.1);
	}
}
.components-button.kadence-background-icon-indicate {
	width: 50px;
	height: 50px;
	overflow: hidden;
	border-radius: 50%;
	position: relative;
    transform: scale(1);
    transition: transform .1s ease;
    border-radius: 50%;
    padding: 0;
	background-image: linear-gradient(45deg, #ddd 25%, transparent 0), linear-gradient(-45deg, #ddd 25%, transparent 0), linear-gradient(45deg, transparent 75%, #ddd 0), linear-gradient(-45deg, transparent 75%, #ddd 0);
	border: 1px solid #dadada;
    background-size: 10px 10px;
	background-position: 0 0,0 5px,5px -5px,-5px 0;
	.component-color-indicator.kadence-advanced-color-indicate {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		margin: 0;
		display: block;
		position: absolute;
		border: 0;
		top:0;
	}
	> .dashicon {
		position: absolute;
		transform: translate(-50%, -50%);
		left: 50%;
		top: 50%;
		color: white;
		background: rgba(0,0,0,.6);
		border-radius: 100%;
		width: 16px;
		height: 16px;
		border: 1px solid rgba(0,0,0,.1);
	}
	img.kadence-background-image-preview {
		display: flex;
		object-fit: cover;
		min-width: 100%;
		min-height: 100%;
		position: absolute;
		top: 0;
	}
}
.components-button.kadence-background-icon-indicate:hover {
	box-shadow: none !important;
}
.kadence-control-field.kadence-color-control {
    display: flex;
	.customize-control-title {
		flex-grow: 2;
	}
}
.components-popover.kadence-popover-color .components-popover__content {
    padding: 10px 10px 0px;
    box-sizing: initial;
    background: rgb(255, 255, 255);
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 8px 16px;
	.sketch-picker {
		padding:0 0 5px  !important;
		box-shadow: none !important;
		border-radius: 0px  !important;
	}
	.attachment-media-view {
		margin-top: 10px;
		margin-bottom: 10px;
	}
}
// .kadence-background-picker-wrap .kadence-popover-color .components-popover__content{
// 	.kadence-background-color-wrap {
// 		display: flex;
// 		flex-direction: row-reverse;
// 		justify-content: space-between;
// 	}
// 	.kadence-swatches-wrap {
// 		flex-direction: column;
// 		padding: 0 10px 0 0 !important;
// 		border-top: 0 !important;
// 		border-right: 1px solid rgb(238, 238, 238);
// 	}
// 	.actions .button:last-child {
// 		margin-left: 10px;
// 	}
// }
.kadence-swatches-wrap .kadence-swatche-item-wrap:hover {
	transform: scale(1.2) !important;
}
.kadence-swatches-wrap .kadence-swatche-item-wrap .kadence-swatch-item {
    background-image: linear-gradient(45deg, #ddd 25%, transparent 0),linear-gradient(-45deg, #ddd 25%, transparent 0),linear-gradient(45deg, transparent 75%, #ddd 0),linear-gradient(-45deg, transparent 75%, #ddd 0);
    background-size: 10px 10px;
	background-position: 0 0,0 5px,5px -5px,-5px 0;
	padding: 0;
    display: flex;
	justify-content: center;
	.dashicon {
		display: none;
	}
}
.kadence-swatches-wrap .kadence-swatche-item-wrap .kadence-swatch-item.swatch-active {
	box-shadow: 0 0 0 8px inset !important;
	.dashicon {
		display: block;
		color: white;
		background: rgba(0,0,0,.6);
		width: 16px;
		height: 16px;
		border: 1px solid rgba(0,0,0,.1);
		border-radius: 100%;
	}
}
.components-button.kadence-color-icon-indicate > .dashicon {
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
	color: white;
    background: rgba(0,0,0,.6);
    border-radius: 100%;
    width: 16px;
    height: 16px;
    border: 1px solid rgba(0,0,0,.1);
}
.kadence-color-picker-wrap {
    margin-left: 5px;
}
.customize-control-kadence_measure_control input[type=number] {
    padding: 0 4px;
}
@media ( max-width: 1900px ) {
.kadence-palette-colors .components-button.kadence-color-icon-indicate .component-color-indicator.kadence-advanced-color-indicate {
    width: 26px;
    height: 26px;
}
.kadence-palette-colors .kadence-color-picker-wrap {
    margin-left: 2px;
}
.kadence-typography-control .typography-button-wrap>button.components-button.kadence-typography-preview-indicate {
    padding: 0 2px;
}
.kadence-typography-control .typography-button-wrap>button.components-button {
    padding: 0 4px;
}
.customize-control-kadence_measure_control input[type=number] {
    padding: 0 2px;
}
}
@media ( max-width: 1900px ) {
	.kadence-palette-colors .components-button.kadence-color-icon-indicate .component-color-indicator.kadence-advanced-color-indicate {
		width: 24px;
		height: 24px;
	}
}
@media ( max-width: 1400px ) {
	.kadence-palette-colors .components-button.kadence-color-icon-indicate .component-color-indicator.kadence-advanced-color-indicate {
		width: 22px;
		height: 22px;
	}
	.customize-control-kadence_measure_control input[type=number] {
		padding: 0px;
	}
}
.kadence-palette-colors .kadence-color-picker-wrap:first-child {
    margin-left: 0;
}
.kadence-popover-tabs.kadence-background-tabs .components-tab-panel__tabs {
    display: flex;
    border-bottom: 1px solid #dadada;
    margin-top: -5px;
    margin-bottom: 15px;
}
.kadence-palette-import-wrap {
    float: left;
}

.kadence-palette-popover-tabs {
	width: 350px;
	max-height: 420px;
}
.kadence-palette-popover-tabs .components-tab-panel__tabs-item {
    display: flex;
    flex: 1;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    font-style: normal;
    height: 36px;
    text-transform: uppercase;
    border: 0;
    border-bottom: 4px solid transparent;
    border-radius: 0;
    margin-bottom: -1px;
    opacity: 0.6;
}
.kadence-palette-popover-tabs .components-tab-panel__tabs {
    display: flex;
    border-bottom: 1px solid #dadada;
    margin-top: -5px;
    margin-bottom: 15px;
}
.kadence-palette-popover-copy-paste .components-popover__content {
    padding: 8px;
    box-sizing: initial;
    background: #fff;
    border-radius: 4px;
    box-shadow: rgba(0,0,0,0.15) 0px 8px 16px;
	width: 100%;
    box-sizing: border-box;
	.kadence-palette-popover-tabs {
		width: 100%;
	}
}
.kadence-palette-popover-tabs .components-tab-panel__tabs-item.active-tab {
    border-bottom-color: #007cba;
    opacity: 1;
    box-shadow: none;
}

.kadence-palette-popover-tabs .components-button.kadence-palette-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 5px;
    margin-bottom: 5px;
    border:1px solid transparent;
    border-radius:4px;
}
.kadence-palette-popover-tabs .components-button.kadence-palette-item:hover {
    border-color:#777;
}
.kadence-popover-tabs.kadence-background-tabs .components-tab-panel__tabs .components-button {
    display: flex;
    flex: 1;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
	font-style: normal;
	height:36px;
	text-transform: uppercase;
	border: 0;
    border-bottom: 4px solid transparent;
	border-radius: 0;
	margin-bottom: -1px;
	opacity: 0.6;
	&:focus {
		outline: 0;
		box-shadow: none;
	}
	&:hover {
		box-shadow: none !important;
		opacity: 1;
		border-bottom: 4px solid #dadada;
	}
	&.active-tab {
		border-bottom-color: $color-primary;
		opacity: 1;
	}
}
.components-popover__content .kadence-radio-container-control {
    padding: 10px 0;
    background: white;
}
.kadence-control-field .kadence-background-tabs .customize-control-title {
    padding-top: 10px;
    font-size: 12px;
    display: block;
}
.kadence-control-field.kadence-background-control .kadence-responsive-control-bar .floating-controls {
    display: flex;
    align-items: center;
    margin-left: 0px;
}
.kadence-control-field.kadence-background-control .kadence-responsive-control-bar .customize-control-title {
    flex-grow: 1;
}
.kadence-control-field.kadence-background-control .kadence-responsive-controls-content {
    display: flex;
    justify-content: flex-end;
}
.kadence-control-field.kadence-palette-control.kadence-color-control {
    display: block;
}
.kadence-palette-header {
	display: flex;
	align-items: center;
}
.kadence-palette-colors {
    display: flex;
    padding: 20px 0 0;
    justify-content: space-between;
}
.kadence-palette-header .components-button-group .components-button.is-tertiary {
    color: #A0AEC0;
    border:1px solid #A0AEC0;
	height:30px;
	font-size: 12px;
	padding: 0 4px;
	box-shadow: none;
}
@media ( max-width: 1900px ) {
.kadence-palette-header .components-button-group .components-button.is-tertiary {
font-size: 10px;
padding: 0 2px;
letter-spacing: -0.4px;
}
}
@media ( max-width: 1601px ) {
.kadence-control-field .customize-control-title {
	font-size: 13px;
}
}
.kadence-palette-header .components-button-group .components-button.is-tertiary.active-palette {
    color: #fff;
    border:1px solid #007cba;
	background: #007cba;
}
.kadence-border-control {
	.kadence-responsive-controls-content {
		display: flex;
		justify-content: flex-end;
		input.components-text-control__input {
			border: 1px solid #e2e4e7;
			width: 60px;
		}
		.kadence-color-picker-wrap {
			margin-right: 5px;
			margin-left: 0;
			justify-content: flex-end;
			align-items: center;
			display: flex;
		}
		.color-button-wrap {
			display: inline-flex;
		}
	}
}
.kadence-units .components-button {
    padding-top: 2px;
    padding-bottom: 2px;
}
.kadence-typography-control {
	.kadence-typography-controls {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		margin-top: 5px;
	}
	.color-button-wrap {
		display: flex;
	}
	.customize-control-title {
		flex-grow: 1;
	}
	.components-popover.kadence-popover-typography > .components-popover__content {
		min-width: 340px;
		min-height: 339px;
		> div {
			min-height: 339px;
		}
	}
	.kadence-typography-tabs .customize-control-title {
		padding-top: 0;
	}
	.kadence-typography-tabs .kadence-range-control {
		padding-top:10px
	}
	.kadence-transform-controls {
		width: 50%;
		padding-right: 10px;
		padding-bottom: 5px;
	}
	.kadence-transform-controls .components-button-group {
		padding: 0 0 10px 0;
		button.components-button.is-tertiary {
			height: 30px;
		}
	}
	.kadence-font-family-list-wrapper {
		padding-top: 35px;
	}
	.components-button-group.kadence-font-family-list {
		display: flex;
		max-height: 259px;
		overflow: scroll;
		flex-direction: column;
		.kadence-font-family-choice {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: space-evenly;
			border-radius: 0;
			margin: 0;
			width: 340px;
			height: 36px;
			min-height: 36px;
			border-bottom: 1px solid #e2e4e7;
			white-space: nowrap;
			box-shadow: none;
			color: $color-gray-800;
			.preview-text {
				font-size: 16px;
				white-space: nowrap;
				color: $color-gray-800;
				line-height: 30px;
			}
			&.active-radio, &.active-radio:hover, &.active-radio:focus {
				background: $color-primary !important;
				color: white !important;
				outline: 0 !important;
				box-shadow: none !important;
				.preview-text {
					color: white;
				}
			}
		}
	}
	.components-button-group.kadence-font-variant-list {
		max-height: 294px;
		display: flex;
		flex-direction: column;
		overflow: scroll;
		.kadence-font-variant-choice {
			display: flex;
			box-shadow: none;
			border-radius: 0;
			margin: 0;
			min-height: 36px;
			border-bottom: 1px solid #e2e4e7;
			white-space: nowrap;
			color: $color-gray-800;
			&.active-radio, &.active-radio:hover, &.active-radio:focus {
				background: $color-primary !important;
				color: white !important;
				outline: 0 !important;
				box-shadow: none !important;
			}
		}
	}
	.kadence-font-family-search {
		position: absolute;
		display: flex;
		top: 0;
		left: 0;
		right: 0;
		background: white;
		.components-base-control {
			display: flex;
			flex-grow: 1;
			.components-base-control__field {
				display: flex;
				margin-bottom: 5px;
				flex-grow: 1;
				input {
					padding-right:40px;
				}
			}
		}
		.kadence-clear-search {
			position: absolute;
			right: 0;
			z-index:100;
			height: 30px;
			border: 0 !important;
			background: transparent !important;
			box-shadow: none !important;
		}
	}
	.kadence-typography-tabs .components-tab-panel__tab-content {
		position: relative;
	}
	button.components-button.kadence-typography-family-indicate {
		max-width: 100px;
		white-space: nowrap;
		overflow: hidden;
	}
	.typography-button-wrap > button.components-button {
		border: 1px solid #e2e4e7;
		margin-left: 2px;
		background-color: #fff;
	}
	.kadence-popover-typography-single-item {
		position: relative;
		.components-button-group.kadence-font-family-list {
			max-height: 304px;
		}
	}
}
.customize-control-kadence_typography_control .kadence-control-field button.components-button.kadence-reset {
    height: 36px;
    top: 0;
}
.kadence-preview-font {
    padding: 10px;
	background: linear-gradient(45deg, #ddd 25%, transparent 0),linear-gradient(-45deg, #ddd 25%, transparent 0),linear-gradient(45deg, transparent 75%, #ddd 0),linear-gradient(-45deg, transparent 75%, #ddd 0);
    background-size: 10px 10px;
    background-position: 0 0,0 5px,5px -5px,-5px 0;
	margin-top: 10px;
	line-height: 1.2;
}
.kadence-select-units select.components-select-control__input {
    width: 100%;
    margin: 0 0 2px 0;
    border: 1px solid #e2e4e7;
}
.kadence-control-field.kadence-title-control {
    background: #f9f9f9;
    margin-bottom: -13px;
    margin-top: -17px;
    margin-left:-24px;
    margin-right:-24px;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    padding: 12px 20px;
}

.kadence-control-field.kadence-title-control .customize-control-title {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: .3px;
}

.kadence-locked .components-button.is-single {
    border: 1px solid #e2e4e7;
    background-color: #fff;
    display: flex;
    height: 30px;
}
.kadence-locked .components-button svg {
    width: 16px;
}
.measure-input-wrap {
    flex-grow: 0;
    display: flex;
    flex-direction: column;
    padding-right:5px
}
.measure-input-wrap small {
	padding-left:3px;
}
.measure-input-wrap input.measure-inputs {
    border: 1px solid #e2e4e7;
}
.kadence-radio-container-control button.components-button.is-tertiary svg {
    width: 100%;
    height: 100%;
    max-height: 100%;
}
.kadence-radio-icon-control.kadence-two-col .components-button-group.kadence-radio-container-control .components-button.is-tertiary svg {
	height: 60px;
}
.kadence-radio-container-control button.components-button .kadence-radio-icon {
    width: 100%;
    height: 100%;
}
#customize-control-page_layout .components-button-group.kadence-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}
#customize-control-page_layout .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 10px;
    margin: 0;
    min-height: 80px;
}
#customize-control-page_title_layout .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-height: 90px;
    padding: 10px;
}
#customize-control-page_title_layout .kadence-radio-container-control button.components-button.is-tertiary:not(:first-child) {
    margin-left: 10px;
}
.kadence-radio-dashicon {
    max-width: 20px;
}

.kadence-sorter-item-panel-header {
    display: flex;
	width: 100%;
	cursor: grab;
	align-items: center;
	border-bottom: 1px solid #A0AEC0;
	margin-bottom: -1px;
	.kadence-sorter-title {
		flex-grow: 2;
		padding: 0 8px;
		font-weight: bold;
	}
	.kadence-sorter-visiblity {
		border-radius: 0;
		height: 36px;
		border-right: 1px solid #A0AEC0;
		.dashicon {
			width: 14px;
			height: 14px;
			font-size: 14px;
		}
	}
	.kadence-sorter-item-expand {
		border-radius: 0;
		position: relative;
		height: 36px;
		border-left: 0;
		&:before {
			content: '';
			position: absolute;
			left: 0;
			height: 50%;
			background: #A0AEC0;
			width: 1px;
		}
		&:focus {
			&:before {
				opacity: 0;
			}
		}
		.dashicon {
			width: 14px;
			height: 14px;
			font-size: 14px;
		}
	}
}

.kadence-sorter-drop .kadence-sorter-item {
    line-height: 28px;
    height: auto;
    background: white;
    position: relative;
    border: 1px solid #A0AEC0;
    white-space: nowrap;
    position: relative;
    margin: 0 0 4px;
    padding: 0px;
    border-radius: 3px;
}
.kadence-sorter-drop {
    display: flex;
    flex-direction: column;
}
.kadence-sorter-item-panel-content {
	padding: 10px;
	.components-base-control__field {
		display: flex;
		flex-direction: column;
	}
	.components-button.kadence-sorter-item-remove {
		color: #b52727;
	}
}
.sortable-style-tabs {
	.components-tab-panel__tabs {
		display: flex;
		border-bottom: 1px solid #dadada;
		margin-bottom: 15px;
		.components-button {
			display: flex;
			-webkit-box-flex: 1;
			flex: 1;
			justify-content: center;
			font-size: 11px;
			font-weight: 600;
			font-style: normal;
			height:36px;
			text-transform: uppercase;
			border: 0;
			border-bottom: 4px solid transparent;
			border-radius: 0;
			margin-bottom: -1px;
			opacity: 0.6;
			box-shadow: none;
			&.active-tab {
				border-bottom-color: #007cba;
				opacity: 1;
			}
		}
	}
}
.kadence-social-add-area {
    display: flex;
}

.kadence-social-add-area .components-base-control {
    flex-grow: 1;
    padding-right: 10px;
}

.kadence-social-add-area .components-base-control .components-base-control__field {
    margin-bottom: 0;
}

.kadence-social-add-area .kadence-sorter-add-item {
	height: 32px;
	line-height: normal;
	svg, .dashicons {
		width: 14px;
		height: 14px;
		font-size: 14px;
		margin-top: 2px;
	}
}
.kadence-sorter-row {
    margin-bottom: 16px;
}
.kadence-sorter-item-panel-content .components-button.button-add-media {
    display:block;
    margin-bottom:20px;
    height: auto;
}
.social-custom-image {
    display: flex;
	margin-bottom: 20px;
	align-items: center;
	justify-content: space-around;
	.components-button.remove-image.is-destructive {
		color: #b52727;
	}
}
.kadence-social-image {
    max-width: 50px;
    padding-right: 20px;
}
.kadence-sorter-item-panel-content .kadence-radio-container-control button.components-button.is-tertiary {
    padding: 4px;
}
@media ( max-width: 1760px ) {
	.measure-input-wrap {
		padding-right: 2px;
	}
	.measure-input-wrap input.measure-inputs {
		padding: 0 2px;
	}

	.kadence-locked .components-button.is-single {
		padding: 0 2px;
	}
	.kadence-range-control button.components-button {
		padding: 0 2px;
	}

	.components-button.has-icon.has-text svg {
		margin-right: 3px;
		max-width: 20px;
	}
	.kadence-locked .components-button.is-single svg {
		width: 14px;
	}
}
.kadence-post-title-sorter .kadence-sorter-item-panel-content .components-toggle-control .components-base-control__field {
    flex-direction: row;
}
.kadence-meta-sorter .kadence-radio-container-control button.components-button.is-tertiary svg {
    max-width: 12px;
    margin: 0 auto;
}
// .kadence-sorter-item-panel-content .components-range-control .components-base-control__field {
//     flex-direction: row;
// }
.components-toggle-control .components-base-control__field .components-toggle-control__label {
    white-space: normal;
}
.kadence-sorter-item-panel-content .kadence-radio-container-control {
	margin-bottom: 10px;
}
.sorter-sub-option {
    padding: 12px 12px 0px;
    border: 1px solid #bbb;
    margin-bottom: 12px;
}
.meta-label-input-control {
    display: flex;
    margin-bottom: 6px;
}
.kadence-label-visiblity svg {
    width: 14px;
}
.components-button.kadence-label-visiblity {
    height: 30px;
}
.label-is-hidden .components-text-control__input {
    opacity: 0.2;
    pointer-events: none;
}
.kadence-radio-icon-control.kadence-three-col .components-button-group.kadence-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}
.kadence-radio-icon-control.kadence-three-col .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 10px;
    margin: 0;
	min-height: 90px;
	svg {
		max-width: 50px;
	}
}
.kadence-radio-icon-control.kadence-two-col .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-height: 90px;
    padding: 10px;
}
.kadence-radio-icon-control .components-button-group.kadence-radio-container-control .components-button.btn-flex-col.is-tertiary {
	flex-direction: column;
	font-size: 10px;
	.kadence-radio-icon {
		margin-bottom: 3px;
		display:block;
	}
}
.kadence-radio-icon-control.kadence-two-col .kadence-radio-container-control button.components-button.is-tertiary:not(:first-child) {
    margin-left: 10px;
}
.kadence-row-layout-control .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 5px;
    margin: 0;
    min-height: 40px;
}
.kadence-row-layout-control .components-button-group.kadence-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}
.kadence-row-layout-control .components-button-group.kadence-radio-container-control .components-button.is-tertiary.active-radio {
    background: white
}
.kadence-row-layout-control .components-button-group.kadence-radio-container-control .components-button.is-tertiary.active-radio svg rect {
    fill: #007cba;;
}
#customize-control-footer_widget1_tabs {
    display: block !important;
}
.typography-button-wrap .components-button {
    height: 36px;
}
.kadence-radio-icon-control.kadence-three-col-short .components-button-group.kadence-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}
.kadence-radio-icon-control.kadence-three-col-short .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 0px;
    margin: 0;
    height: 30px;
}
.kadence-sorter-no-sorting .kadence-sorter-item {
    margin-bottom: 12px;
}
.kadence-sorter-no-sorting  .kadence-sorter-item-panel-header {
    cursor: default;
}
.components-button-group.kadence-featured-image-ratio {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}

.kadence-sorter-item-panel-content .kadence-featured-image-ratio button.components-button.is-tertiary {
    padding:0;
    height:30px;
    margin:0;
}
.kadence-sorter-item-panel-content .kadence-radio-container-control button.components-button.is-tertiary svg {
    max-width:14px;
    margin:0 auto;
}
// Tweeks:
#customize-theme-controls .accordion-section-content {
    color: #2D3748;
}
.kadence-sorter-item-panel-content .components-range-control .components-base-control__field input.components-range-control__number {
    width: auto;
}
.kadence-popover-social-list .components-button-group.kadence-radio-container-control {
    flex-wrap: wrap;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap:5px;
    padding-bottom: 15px;
    padding-top: 5px;
}
.kadence-popover-social-list .components-button-group.kadence-radio-container-control .components-button.social-radio-btn.is-tertiary {
    min-width: 80px;
    margin: 0;
    padding: 0;
    font-size: 10px;
}
.radio-icon-padding  .kadence-radio-container-control button.components-button.is-tertiary {
    padding: 10px 0;
}
.kadence-sorter-drop-social_item_group .kadence-sorter-item-panel-header .kadence-sorter-visiblity {
	background: #f9f9f9;
    border: 0;
}
.kadence-sorter-drop-social_item_group .kadence-sorter-item-panel-header .kadence-sorter-visiblity svg {
    max-width: 14px;
}
.kadence-sorter-drop-social_item_group .kadence-sorter-item-panel-header .kadence-sorter-visiblity.item-is-hidden {
    opacity: 0.2;
}
.kadence-link-color-control .components-base-control__field {
    margin-bottom: 0;
}
.customize-control-kadence_borders_control .kadence-border-control>.kadence-responsive-controls-content {
    display: block;
}
.customize-control-kadence_borders_control .kadence-responsive-controls-content.kadence-border-single-item {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ddd;
}
.customize-control-kadence_borders_control .kadence-responsive-controls-content.kadence-border-single-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: 0;
}
span.border-icon {
    display: flex;
    width: 30px;
    align-items: center;
    flex-grow: 1;
}
span.border-icon svg {
    width: 24px;
    height: 24px;
}
.components-custom-gradient-picker .components-base-control__label {
	padding-top: 10px;
   font-size: 12px;
   display: block;
   font-weight: 600;
   letter-spacing: 0.1px;
   line-height: 18px;
}
.kadence-background-tabs .components-circular-option-picker .components-circular-option-picker__custom-clear-wrapper {
    justify-content: flex-start;
}
.components-custom-gradient-picker .components-custom-gradient-picker__gradient-bar {
   box-sizing: border-box;
}
.kadence-color-picker-wrap .kadence-popover-color .components-popover__content {
    min-width: 300px;
    min-height: 320px;
    max-height: 60vh;
}
.kadence-color-picker-wrap .kadence-popover-tabs .components-tab-panel__tab-content {
    position: relative;
}

// Fake Tabs
#accordion-section-kadence_customizer_sidebar_design, #accordion-section-kadence_customizer_cart_design, #accordion-section-kadence_customizer_product_layout_design, #accordion-section-kadence_customizer_course_layout_design, #accordion-section-kadence_customizer_lesson_layout_design, #accordion-section-kadence_customizer_course_archive_design, #accordion-section-kadence_customizer_header_popup_design, #accordion-section-kadence_customizer_llms_membership_archive_design, #accordion-section-woocommerce_product_catalog_design, #accordion-section-kadence_customizer_post_archive_design, #accordion-section-kadence_customizer_sfwd_courses_layout_design, #accordion-section-kadence_customizer_post_layout_design,#accordion-section-kadence_customizer_header_transparent_design, #accordion-section-woocommerce_store_notice_design, #accordion-section-kadence_customizer_scroll_up_design, li#accordion-section-kadence_customizer_courses_layout_design, #accordion-section-kadence_customizer_sfwd_groups_layout_design, #accordion-section-kadence_customizer_sfwd_essays_layout_design, #accordion-section-kadence_customizer_search_design, #accordion-section-kadence_customizer_sfwd_lesson_layout_design, #accordion-section-kadence_customizer_sfwd_topic_layout_design, #accordion-section-kadence_customizer_sfwd_grid_layout_design, li.accordion-section.control-section-design-hidden, #accordion-section-kadence_customizer_sfwd_quiz_layout_design, #accordion-section-kadence_customizer_general_404_design, #accordion-section-kadence_customizer_sfwd_courses_archive_layout_design, #accordion-section-kadence_customizer_tribe_events_layout_design, #accordion-section-kadence_customizer_courses_archive_layout_design {
    display: none !important;
}
.kadence-prevent-transition {
    transition: none !important;
}
// Graditent overide.
.kadence-popover-color .components-circular-option-picker {
    position: relative;
    z-index: 10000000;
}
.kadence-popover-color .components-circular-option-picker .components-popover.components-custom-gradient-picker__color-picker-popover {
    top:40px !important;
    left:30px !important;
    bottom:auto !important;
}
.kadence-popover-color .components-circular-option-picker .components-popover.components-custom-gradient-picker__color-picker-popover > div {
    top: 100%;
    bottom: auto;
}
.kadence-popover-color .components-circular-option-picker .components-popover.components-custom-gradient-picker__color-picker-popover::before, .kadence-popover-color .components-circular-option-picker .components-popover.components-custom-gradient-picker__color-picker-popover::after {
    display:none;
}
.kadence-tiny-text .kadence-radio-container-control button.components-button.is-tertiary {
    font-size:9px;
}
#customize-control-logo_layout .kadence-radio-container-control button.components-button.is-tertiary {
    font-size:9px;
}
.kadence-typography-control .typography-button-wrap>button.components-button.kadence-typography-size-indicate {
    min-width: 46px;
}
.kadence-builder-is-active .wp-full-overlay.collapsed #customize-theme-controls #sub-accordion-section-kadence_customizer_header_builder, .kadence-footer-builder-is-active .wp-full-overlay.collapsed #customize-theme-controls #sub-accordion-section-kadence_customizer_footer_builder {
    transform: translateY(100%) !important;
    overflow:hidden;
}
.kadence-builder-is-active .wp-full-overlay.collapsed #customize-preview, .kadence-footer-builder-is-active .wp-full-overlay.collapsed #customize-preview {
    bottom:0 !important;
}
.kadence-builder-areas .kadence-builder-group-horizontal .kadence-builder-drop-left_center, .kadence-builder-areas .kadence-builder-group-horizontal .kadence-builder-drop-right_center {
	display:none;
}
.kadence-builder-areas.has-center-items .kadence-builder-drop-left_center, .kadence-builder-areas.has-center-items .kadence-builder-drop-right_center {
	display:flex;
}
.kadence-radio-icon-control.kadence-two-forced .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    margin:0;
}

.kadence-radio-icon-control.kadence-two-forced .kadence-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}
.customize-control-kadence_borders_control button.components-button.reset.kadence-reset {
    margin-top: 5px;
}
.kadence-units .components-toolbar .components-button:before {
    display: none;
}
li#customize-control-kadence_color_palette .customize-control-description {
    text-align: right;
    margin-top: 10px;
}
.kadence-radio-icon-control.kadence-three-col.kadence-auto-height .components-button-group.kadence-radio-container-control .components-button.is-tertiary {
    min-height: 0;
}
@media ( max-width: 1700px ) {
.kadence-tiny-text .kadence-radio-container-control button.components-button.is-tertiary {
font-size: 7px;
}
}
p.kt-box-shadow-title {
	text-align: center;
	margin-bottom: 0;
}
.kadence-boxshadow-control .kadence-responsive-controls-content input.components-text-control__input {
    border: 1px solid #e2e4e7;
	width: 50px;
	padding-left: 2px;
}
.kt-box-inset-settings {
    padding-top: 10px;
}
#customize-control-google_subsets .kadence-radio-container-control {
	display:grid;
	grid-template-columns: 1fr 1fr 1fr;
	grid-gap: 10px;
 }
 .kadence-sorter-title {
    overflow: hidden;
}
 #customize-control-google_subsets .kadence-radio-container-control button.components-button.is-tertiary {
	 margin: 0;
	 font-size:9px
 }
 .kadence-popover-tabs .components-custom-gradient-picker__gradient-bar:not(.has-gradient) {
    opacity: 1;
}
.components-dropdown__content.components-custom-gradient-picker__color-picker-popover .components-popover__content>div {
    padding: 0;
}
.kadence-sorter-no-sorting .kadence-sorter-item {
    background: #fff;
    border: 0;
    line-height: 42px;
}

.kadence-sorter-no-sorting .kadence-sorter-item-panel-header {
    border-bottom: 0;
}
.kadence-sorter-drop:not(.kadence-sorter-no-sorting) .kadence-sorter-item {
    border-left: 3px solid #007cba;
}
.kadence-sorter-drop:not(.kadence-sorter-no-sorting) .kadence-move-icon {
    margin-left: -3px;
    margin-right:5px;
    transform: rotate(90deg);
    cursor: grab;
    width: 18px;
    opacity: 0.7;
}
.kadence-sorter-drop:not(.kadence-sorter-no-sorting) .kadence-sorter-item-panel-header .kadence-sorter-visiblity {
    border-left: 1px solid #A0AEC0;
}
.rtl .kadence-control-field button.components-button.kadence-reset {
    margin-right: -20px;
    margin-left: 0;
}
// Color Palette Control Fix.
.kadence-color-picker-wrap {
	> span + .color-button-wrap .components-button.kadence-color-icon-indicate {
		transform: scale(1.15);
		box-shadow: 0 0 0 1.5px #007cba;
		box-shadow: 0 0 0 1.5px var(--wp-admin-theme-color);
		outline: 1px solid transparent;
	 }
	.components-popover.kadence-popover-color.is-from-left {
		left: 40px !important;
	}
	.components-popover.kadence-popover-color {
		animation: kadence-animate__appear-animation .1s cubic-bezier(0,0,.2,1) 0s
	}
	 .components-popover.kadence-popover-color.is-from-right {
		left: 40px !important;
	}
	.components-popover.kadence-popover-color.is-from-right .components-popover__content {
		right: auto;
	}
}
// .kadence-color-picker-wrap {
// 	.kadence-popover-color .components-popover__content > div:not(.kadence-swatches-wrap):not(.kadence-picker) {
// 		min-height: 300px;
// 	}
// }
.kadence-background-picker-wrap {
	> span + .background-button-wrap .components-button.kadence-background-icon-indicate {
		transform: scale(1.15);
		box-shadow: 0 0 0 1.5px #007cba;
		box-shadow: 0 0 0 1.5px var(--wp-admin-theme-color);
		outline: 1px solid transparent;
	}
	.components-popover.kadence-popover-color.is-from-left {
		left: 40px !important;
	}
	.components-popover.kadence-popover-color {
		animation: kadence-animate__appear-animation .1s cubic-bezier(0,0,.2,1) 0s
	}
	 .components-popover.kadence-popover-color.is-from-right {
		left: 40px !important;
	}
	.components-popover.kadence-popover-color.is-from-right .components-popover__content {
		right: auto;
	}
}
 @keyframes kadence-animate__appear-animation{0%{opacity:0;}to{opacity:1;}}
 .rtl .kadence-color-picker-wrap .components-popover.kadence-popover-color.is-from-right, .rtl .kadence-color-picker-wrap .components-popover.kadence-popover-color.is-from-left, .rtl .kadence-background-picker-wrap .components-popover.kadence-popover-color.is-from-left, .rtl .kadence-background-picker-wrap .components-popover.kadence-popover-color.is-from-right {
    left: auto !important;
}
.kadence-builder-areas {
    padding-left: 20px;
}
.kadence-builder-areas.popup-vertical-group {
	padding-left: 0;
	padding-top: 26px;
}
.kadence-builder-areas.popup-vertical-group button.components-button.kadence-row-actions {
    top:0;
    opacity: 1;
    width:calc( 100% - 20px );
}
.kadence-builder-areas button.components-button.kadence-row-actions {
    left: 0;
}
.kadence-builder-areas button.components-button.kadence-row-left-actions {
    position: absolute;
    left: 0;
    height: 100%;
    min-width: 0;
    width: 20px;
    padding: 0;
    color: #c8dbe4;
    box-shadow: none !important;
    background: #007cba;
    border-radius: 0;
	font-size: 10px;
	&:hover {
		color: white;
	}
}
.kadence-builder-areas.popup-vertical-group button.components-button.kadence-row-left-actions {
    display: none;
}
.kadence-builder-areas button.components-button.kadence-row-left-actions .dashicon {
	width: 12px;
	height: 12px;
    font-size: 12px;
	margin:0;
}
.kadence-background-picker-wrap .components-popover.kadence-popover-color.components-custom-gradient-picker__color-picker-popover .components-popover__content {
    left: 0;
    transform: none;
}
.kadence-color-picker-wrap .kadence-background-tabs .components-popover.components-custom-gradient-picker__color-picker-popover .components-popover__content {
    left: 0;
    transform: none;
	min-width: 240px;
}
.kadence-radio-icon-control.kadence-two-grid .kadence-radio-container-control {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr) );
    column-gap: 0.5rem;
    row-gap: 0.5rem;
}
.kadence-radio-icon-control.kadence-two-grid .kadence-radio-container-control button.components-button.is-tertiary {
    margin: 0;
}
// Temp Fix for Core issue.
.customize-control-title .disabled-element-wrapper, .kadence-control-field .disabled-element-wrapper {
    opacity: 0;
}
// Font Pairing
.kadence-font-pair-popover > .components-popover__content {
    padding: 0 12px 12px;
}
.components-button-group.kt-font-pair-group {
    min-width: 290px;
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 4px;
}
.kadence-font-pair-wrap {
    margin-right: 0;
    margin-left: auto;
}
.components-button-group.kt-font-pair-group button.components-button.kt-font-pair-btn {
    height: auto;
	text-align: center;
    flex-direction: column;
	justify-content: center;
	img {
		max-height: 40px;
		height: auto;
		width: auto;
	}
	span {
		font-size: 11px;
		font-weight: normal;
	}
	&.state-confirm {
		background: rgba(0, 124, 186, .1);
	}
}

// Issues with 6.1 because of popover switching to absolute positioning.
#customize-theme-controls .customize-pane-child.accordion-section-content, #customize-theme-controls .customize-pane-child.accordion-sub-container {
	min-height: 100%;
}
.components-popover.kadence-customizer-popover:not(.components-animate__appear) {
	left: -5px !important;
    right: -5px !important;
    max-width: calc( 100% + 20px );
}
.kadence-color-picker-wrap .kadence-popover-color:not(.components-animate__appear) .components-popover__content {
	min-height: 284px;
}
.kadence-typography-control .components-popover.kadence-popover-typography.kadence-customizer-popover:not(.components-animate__appear)>.components-popover__content {
    max-width: 100%;
    min-width: 100%;
    box-sizing: border-box;
}
.kadence-color-picker-wrap .kadence-popover-color.kadence-customizer-popover:not(.components-animate__appear) .components-popover__content {
	max-width: 100%;
    min-width: 100%;
    box-sizing: border-box;
}
.kadence-background-picker-wrap .kadence-popover-color.kadence-customizer-popover:not(.components-animate__appear) .components-popover__content {
	max-width: 100%;
    min-width: 100%;
    overflow: scroll !important;
    box-sizing: border-box;
    min-height: 330px;
}
.kadence-color-picker-wrap .kadence-popover-color.kadence-popover-color-gradient:not(.components-animate__appear) .components-popover__content {
	min-height: 330px;
	overflow: visible !important;
}
.kadence-customizer-popover:not(.components-animate__appear) .kadence-picker {
    max-width: 100%;
	margin-left:auto;
	margin-right: auto;
}
.kadence-customizer-popover:not(.components-animate__appear) .kadence-swatches-wrap {
	max-width: 300px;
	margin-left:auto;
	margin-right: auto;
}
// New Gradient Control
.components-custom-gradient-picker__item {
	display: block;
	flex:5 1 0%;
	max-height: 100%;
    max-width: 100%;
    min-height: 0px;
    min-width: 0px;
	.kadence-controls-content {
		gap: 12px;
	}
	.kadence-controls-content .components-base-control {
		margin-bottom: 0;
		flex: 10 0 0;
	}
	.kadence-control-toggle-advanced.only-icon {
		flex: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 10px;
		font-weight: 600;
		font-style: normal;
		text-transform: uppercase;
		height: 30px;
		line-height: 1.2;
		border: 1px solid #CBD5E0;
		border-radius: 2px;
		background: transparent;
		color: #4A5568;
		padding: 4px;
		box-shadow: none;
		white-space: normal;
		svg {
			width: 20px;
		}
		&.is-primary {
			border-color: var(--wp-admin-theme-color, #00669b);
			background: var(--wp-admin-theme-color, #00669b);
			color: #fff;
			box-shadow: none;
		}
	}
}
.block-editor-block-inspector .components-custom-gradient-picker__item .kadence-select-large .components-select-control__input {
	height: 40px;
	min-height: 40px;
}
.kadence-gradient-position-control .kadence-gradient-position_header .kadence-gradient-position__label {
	margin: 0px 0px 8px;
	display:block;
}
.kadence-gradient-position-control .components-unit-control-wrapper {
    flex-grow: 1;
}
.kadence-gradient-control .components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-dropdown {
	position: absolute;
    height: 16px;
    width: 16px;
    top: 16px;
    display: flex;
}
.kadence-gradient-control .components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-button {
	height: inherit;
	width: inherit;
	border-radius: 50%;
	padding: 0;
	box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) #fff,0 0 2px 0 rgba(0, 0, 0, 0.25);
	outline: 2px solid transparent;
	position: static;
    top: auto;
}
.kadence-gradient-control {
	.components-custom-gradient-picker__ui-line .components-base-control {
		margin-bottom: 0;
	}
	.components-custom-gradient-picker__ui-line .components-base-control .components-base-control__field {
		margin-bottom: 0;
	}
}
.kadence-gradient-control .components-custom-gradient-picker__gradient-bar {
	border-radius: 2px;
    width: 100%;
    height: 48px;
	margin-bottom: 16px;
	padding-right: 0;

	.components-custom-gradient-picker__markers-container {
		position: relative;
		width: calc(100% - 48px);
		margin-left: auto;
		margin-right: auto;
	}

	.components-custom-gradient-picker__control-point-dropdown {
		position: absolute;
		height: 16px;
		width: 16px;
		top: 16px;
		display: flex;
	}

	.components-custom-gradient-picker__insert-point-dropdown {
		position: relative;

		// Same size as the .components-custom-gradient-picker__control-point-dropdown parent
		height: inherit;
		width: inherit;
		min-width: 16px;
		border-radius: 50%;

		background: #fff;
		padding: 2px;

		color: #111;

		svg {
			height: 100%;
			width: 100%;
		}
	}
}
.kadence-gradient-control .components-angle-picker-control .components-input-control__container .components-input-control__input {
    height: 32px;
    padding-left: 8px;
    padding-right: 8px;
}
.kadence-pop-gradient-color-picker {
    width: 280px;
	max-width: 100%;
}
.components-popover.components-dropdown__content.components-color-palette__custom-color-dropdown-content.kadence-pop-color-popover {
    z-index: 10000000;
    animation: kadence-animate__appear-animation 0.1s cubic-bezier(0, 0, 0.2, 1) 0s;
	.components-popover__content {
		min-height: 0;
	}
}
.wrap-components-custom-gradient-picker {
	position: relative;
	z-index: 11;
}
.components-dropdown__content.components-color-palette__custom-color-dropdown-content .kadence-picker > div:first-child {
    padding-bottom: 25% !important;
}
.kadence-background-picker-wrap .kadence-popover-color .components-popover__content>.kadence-pop-gradient-color-picker {
    min-height: 0;
}
.kadence-background-picker-wrap .components-popover.kadence-popover-color >.components-popover__content {
    overflow: visible;
}
.kadence-sorter-item-panel-content .components-form-toggle {display:flex}

.control-section-kadence_section_pro h3 {
    margin: 0 0 8px 0;
    padding: 1px 0;
    border: 0;
    position: relative;
	a {
		background: #fff;
		display: block;
		padding: 11px 10px 12px 14px;
		text-decoration: none;
		border-left: 4px solid #fff;
		transition: background-color ease-in-out, .15s border-color ease-in-out;
		&:after {
			content: "\f345";
			position: absolute;
			top: 11px;
			right: 10px;
			z-index: 1;
			float: right;
			border: none;
			background: 0 0;
			font: normal 20px / 1 dashicons;
			speak: none;
			display: block;
			padding: 0;
			text-indent: 0;
			text-align: center;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
		}
		&:hover {
			background: #f6f7f7;
			border-left-color: transparent;
		}
	}
}
// Fix because other plugins are breaking the load with bad code that outputs css before the html tag.
#customize-theme-controls .accordion-section-title button.accordion-trigger {
    max-height: fit-content;
}