<?php
/**
 * American Eagle WP MU Plugin Email Class
 *
 * @package AmericaneagleWpMUPlugin\Notifications
 */

namespace AmericaneagleWpMUPlugin\Notifications;

use AmericaneagleWpMUPlugin\BaseAbstract;

/**
 * WordPress Email Functionality.
 */
class Email extends BaseAbstract {

	/**
	 * Hides menu items in WP Admin area.
	 *
	 * @param int $days Days.
	 *
	 * @return void
	 */
	public static function send_notification_email( int $days = 7 ): void {
		
		// Email config.
		$email_list = [
			get_option('admin_email'),
			'<EMAIL>',
			'<EMAIL>',
		];
		$subject    = 'Disallowed Plugin Deletion';
		$headers    = [
			'Content-type: text/html',
		];

		// Remaining days before plugin deletion.
		$remaining = $days !== 7 ? 7 - $days : 7;

		// Check to see if plugins have already been deleted.
		$plugins_names_to_delete = [];
		$current_plugins         = get_plugins();

		foreach( self::BLACKLISTED_PLUGINS as $key => $blocked_plugin ) {
			if ( isset( $current_plugins[ $blocked_plugin ] ) ) {
				$plugins_names_to_delete[] = $key;
			}
		}

		// If no plugins require deletion then just exit.
		if ( empty( $plugins_names_to_delete ) ) {
			return;
		}

		// Send notification email.
		foreach ( $email_list as $email ) {
			$message = 'The following plugins need to be removed as they represent a security risk to <a href="' . get_site_url() . '">' . get_bloginfo( 'name' ) . '</a>.<br>';
			$message .= 'If they are not replaced within ' . absint( $remaining ) . ' days, they will be deleted.<br><br>';

			// Build email list.
			foreach ( $plugins_names_to_delete as $plugin ) {
				$message .= $plugin . '<br>';
			}

			// Send email.
			wp_mail( $email, $subject, $message, $headers );
		}
	}
}
