import { registerPlugin } from '@wordpress/plugins';
const icon = <svg
		xmlns="http://www.w3.org/2000/svg"
		fillRule="evenodd"
		strokeLinejoin="round"
		strokeMiterlimit="2"
		clipRule="evenodd"
		viewBox="0 0 50 40"
	  >
		<path d="M9.857 8.351H29.519V15.874H9.857z"></path>
		<path
		  fillRule="nonzero"
		  d="M10.259 17.908h18.847c.225 0 .41.354.41.786 0 .431-.185.785-.41.785H10.259c-.225 0-.41-.354-.41-.785 0-.432.185-.786.41-.786z"
		></path>
		<path
		  d="M47.109 38.98H2.891a1.9 1.9 0 01-1.898-1.898V2.918A1.9 1.9 0 012.891 1.02h44.218a1.9 1.9 0 011.898 1.898v34.164a1.9 1.9 0 01-1.898 1.898zm-.102-33.614H2.993V36.98h44.014V5.366zm-8.172-2.94a.9.9 0 110 ******* 0 010-1.8zm7.153 0a.9.9 0 110 ******* 0 010-1.8zm-3.538 0a.9.9 0 110 ******* 0 010-1.8z"
		></path>
		<path
		  d="M40.119 13.838l4.705 4.844-10.54 9.899a110.5 110.5 0 01-3.115 1.566 64.17 64.17 0 01-2.948 1.35 32.236 32.236 0 01-1.114.445 13 13 0 01-.794.269 4.38 4.38 0 01-.619.145 1.67 1.67 0 01-.189.018h-.061c-.089-.003-.206-.018-.258-.101-.043-.068-.043-.159-.038-.235l.007-.061a2.98 2.98 0 01.179-.646c.09-.245.193-.485.301-.722.186-.408.387-.809.594-1.206.369-.708.759-1.405 1.157-2.097a104.799 104.799 0 012.183-3.624l10.55-9.844zM30.686 24.71l2.542 2.725-3.053 1.621-1.329-1.217 1.84-3.129zm11.137-12.463l2.23-2.081s1.959-1.222 4.028.819c1.729 1.706.765 3.92.765 3.92l-2.323 2.182-4.7-4.84z"
		></path>
		<path
		  d="M40.152 26.389v7.571h-8.567v-1.649l.108-.045c.987-.415 1.96-.862 2.92-1.336a44.58 44.58 0 001.751-.906l3.788-3.635zm0-15.912l-8.567 8.041V8.421h8.567v2.056z"
		></path>
		<path
		  fillRule="nonzero"
		  d="M28.872 21.063l-.592.557s-.284.383-.716 1.015H10.259a.256.256 0 01-.039-.003.332.332 0 01-.19-.132l-.023-.033c-.01-.014-.018-.029-.027-.043a1.15 1.15 0 01-.124-.436 1.435 1.435 0 01.006-.334c.012-.091.033-.18.066-.266.009-.025.02-.049.031-.073l.032-.059.027-.041a.392.392 0 01.025-.032.308.308 0 01.177-.116.515.515 0 01.039-.004h18.613z"
		></path>
		<path
		  fill="#CDCDCD"
		  fillRule="nonzero"
		  d="M26.519 24.219a47.303 47.303 0 00-.953 1.572H10.259a.172.172 0 01-.039-.004.326.326 0 01-.19-.131.405.405 0 01-.023-.034c-.01-.014-.018-.028-.027-.043a1.156 1.156 0 01-.124-.436 1.426 1.426 0 01.006-.333c.012-.091.033-.181.066-.266.009-.025.02-.049.031-.073l.032-.059.027-.041a.392.392 0 01.025-.032.308.308 0 01.177-.116.172.172 0 01.039-.004h16.26z"
		></path>
		<path
		  fillRule="nonzero"
		  d="M23.417 30.531c-.152.573-.233 1.106-.214 1.571H10.259a.256.256 0 01-.039-.003.328.328 0 01-.19-.132l-.023-.033c-.01-.014-.018-.029-.027-.043a1.162 1.162 0 01-.124-.436 1.436 1.436 0 01.006-.334c.012-.09.033-.18.066-.266.009-.025.02-.049.031-.073l.032-.059.027-.041a.392.392 0 01.025-.032.313.313 0 01.177-.116.256.256 0 01.039-.003h13.158z"
		></path>
		<path
		  fillRule="nonzero"
		  d="M24.701 27.375a22.69 22.69 0 00-.732 1.572h-13.71c-.013 0-.026-.002-.039-.004a.326.326 0 01-.19-.131.405.405 0 01-.023-.034c-.01-.014-.018-.028-.027-.043a1.162 1.162 0 01-.124-.436 1.436 1.436 0 01.006-.334 1.177 1.177 0 01.097-.338c.01-.021.021-.04.032-.059l.027-.042a.698.698 0 01.025-.032.312.312 0 01.177-.115.172.172 0 01.039-.004h14.442z"
		></path>
	  </svg>;
import KadenceThemeLayout from './layout';
registerPlugin( 'kadence-theme-layout', { render: KadenceThemeLayout, icon: icon } );