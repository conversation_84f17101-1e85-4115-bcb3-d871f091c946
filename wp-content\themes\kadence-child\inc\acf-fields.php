<?php
/**
 * ACF Field Groups for North Shore Care Center
 * 
 * This file contains all ACF field group definitions in PHP format.
 * This ensures field groups are version controlled and easily portable.
 * 
 * @package NSC
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Register ACF Field Groups
 * 
 * This function will be called by ACF to register all field groups.
 * Field groups are defined in PHP format for better version control.
 */
add_action('acf/include_fields', function() {
    if ( ! function_exists('acf_add_local_field_group') ) {
        return;
    }

    // TODO: Add field groups here
    // Field groups will be added here after export from database
    
    // Example structure:
    /*
    acf_add_local_field_group(array(
        'key' => 'group_example',
        'title' => 'Example Field Group',
        'fields' => array(
            array(
                'key' => 'field_example',
                'label' => 'Example Field',
                'name' => 'example_field',
                'type' => 'text',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'page',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
    */
});

/**
 * Helper function to check if ACF is active
 * 
 * @return bool
 */
function nsc_is_acf_active() {
    return class_exists('ACF') && function_exists('acf_add_local_field_group');
}

/**
 * Get ACF field value with fallback
 * 
 * @param string $field_name
 * @param mixed $post_id
 * @param mixed $default
 * @return mixed
 */
function nsc_get_field( $field_name, $post_id = false, $default = '' ) {
    if ( ! nsc_is_acf_active() ) {
        return $default;
    }
    
    $value = get_field( $field_name, $post_id );
    return $value !== false ? $value : $default;
}

/**
 * Display ACF field with fallback
 * 
 * @param string $field_name
 * @param mixed $post_id
 * @param mixed $default
 */
function nsc_the_field( $field_name, $post_id = false, $default = '' ) {
    echo nsc_get_field( $field_name, $post_id, $default );
}
