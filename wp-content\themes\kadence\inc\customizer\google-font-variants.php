<?php
          /**
           * Returns an array of Google fonts and their properties for the frontend font selector
           * Generated automatically by GitHub Action
           */

return array('42dot Sans' => array('v' => array('300','regular','500','600','700','800'), 'c' => array('sans-serif')),'ABeeZee' => array('v' => array('regular','italic'), 'c' => array('sans-serif')),'ADLaM Display' => array('v' => array('regular'), 'c' => array('display')),'AR One Sans' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Abel' => array('v' => array('regular'), 'c' => array('sans-serif')),'Abhaya Libre' => array('v' => array('regular','500','600','700','800'), 'c' => array('serif')),'Aboreto' => array('v' => array('regular'), 'c' => array('display')),'Abril Fatface' => array('v' => array('regular'), 'c' => array('display')),'Abyssinica SIL' => array('v' => array('regular'), 'c' => array('serif')),'Aclonica' => array('v' => array('regular'), 'c' => array('sans-serif')),'Acme' => array('v' => array('regular'), 'c' => array('sans-serif')),'Actor' => array('v' => array('regular'), 'c' => array('sans-serif')),'Adamina' => array('v' => array('regular'), 'c' => array('serif')),'Advent Pro' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Afacad' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Afacad Flux' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Agbalumo' => array('v' => array('regular'), 'c' => array('display')),'Agdasima' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Agu Display' => array('v' => array('regular'), 'c' => array('display')),'Aguafina Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Akatab' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Akaya Kanadaka' => array('v' => array('regular'), 'c' => array('display')),'Akaya Telivigala' => array('v' => array('regular'), 'c' => array('display')),'Akronim' => array('v' => array('regular'), 'c' => array('display')),'Akshar' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Aladin' => array('v' => array('regular'), 'c' => array('display')),'Alata' => array('v' => array('regular'), 'c' => array('sans-serif')),'Alatsi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Albert Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Aldrich' => array('v' => array('regular'), 'c' => array('sans-serif')),'Alef' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Alegreya' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Alegreya SC' => array('v' => array('regular','italic','500','500italic','700','700italic','800','800italic','900','900italic'), 'c' => array('serif')),'Alegreya Sans' => array('v' => array('100','100italic','300','300italic','regular','italic','500','500italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Alegreya Sans SC' => array('v' => array('100','100italic','300','300italic','regular','italic','500','500italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Aleo' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Alex Brush' => array('v' => array('regular'), 'c' => array('handwriting')),'Alexandria' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Alfa Slab One' => array('v' => array('regular'), 'c' => array('display')),'Alice' => array('v' => array('regular'), 'c' => array('serif')),'Alike' => array('v' => array('regular'), 'c' => array('serif')),'Alike Angular' => array('v' => array('regular'), 'c' => array('serif')),'Alkalami' => array('v' => array('regular'), 'c' => array('serif')),'Alkatra' => array('v' => array('regular','500','600','700'), 'c' => array('display')),'Allan' => array('v' => array('regular','700'), 'c' => array('display')),'Allerta' => array('v' => array('regular'), 'c' => array('sans-serif')),'Allerta Stencil' => array('v' => array('regular'), 'c' => array('sans-serif')),'Allison' => array('v' => array('regular'), 'c' => array('handwriting')),'Allura' => array('v' => array('regular'), 'c' => array('handwriting')),'Almarai' => array('v' => array('300','regular','700','800'), 'c' => array('sans-serif')),'Almendra' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Almendra Display' => array('v' => array('regular'), 'c' => array('display')),'Almendra SC' => array('v' => array('regular'), 'c' => array('serif')),'Alumni Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Alumni Sans Collegiate One' => array('v' => array('regular','italic'), 'c' => array('sans-serif')),'Alumni Sans Inline One' => array('v' => array('regular','italic'), 'c' => array('display')),'Alumni Sans Pinstripe' => array('v' => array('regular','italic'), 'c' => array('sans-serif')),'Amarante' => array('v' => array('regular'), 'c' => array('display')),'Amaranth' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Amatic SC' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Amethysta' => array('v' => array('regular'), 'c' => array('serif')),'Amiko' => array('v' => array('regular','600','700'), 'c' => array('sans-serif')),'Amiri' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Amiri Quran' => array('v' => array('regular'), 'c' => array('serif')),'Amita' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Anaheim' => array('v' => array('regular','500','600','700','800'), 'c' => array('sans-serif')),'Ancizar Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Ancizar Serif' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Andada Pro' => array('v' => array('regular','500','600','700','800','italic','500italic','600italic','700italic','800italic'), 'c' => array('serif')),'Andika' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Anek Bangla' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Devanagari' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Gujarati' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Gurmukhi' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Kannada' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Latin' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Malayalam' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Odia' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Tamil' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Anek Telugu' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Angkor' => array('v' => array('regular'), 'c' => array('display')),'Annapurna SIL' => array('v' => array('regular','700'), 'c' => array('serif')),'Annie Use Your Telescope' => array('v' => array('regular'), 'c' => array('handwriting')),'Anonymous Pro' => array('v' => array('regular','italic','700','700italic'), 'c' => array('monospace')),'Anta' => array('v' => array('regular'), 'c' => array('sans-serif')),'Antic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Antic Didone' => array('v' => array('regular'), 'c' => array('serif')),'Antic Slab' => array('v' => array('regular'), 'c' => array('serif')),'Anton' => array('v' => array('regular'), 'c' => array('sans-serif')),'Anton SC' => array('v' => array('regular'), 'c' => array('sans-serif')),'Antonio' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Anuphan' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Anybody' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('display')),'Aoboshi One' => array('v' => array('regular'), 'c' => array('serif')),'Arapey' => array('v' => array('regular','italic'), 'c' => array('serif')),'Arbutus' => array('v' => array('regular'), 'c' => array('serif')),'Arbutus Slab' => array('v' => array('regular'), 'c' => array('serif')),'Architects Daughter' => array('v' => array('regular'), 'c' => array('handwriting')),'Archivo' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Archivo Black' => array('v' => array('regular'), 'c' => array('sans-serif')),'Archivo Narrow' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Are You Serious' => array('v' => array('regular'), 'c' => array('handwriting')),'Aref Ruqaa' => array('v' => array('regular','700'), 'c' => array('serif')),'Aref Ruqaa Ink' => array('v' => array('regular','700'), 'c' => array('serif')),'Arima' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('display')),'Arimo' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Arizonia' => array('v' => array('regular'), 'c' => array('handwriting')),'Armata' => array('v' => array('regular'), 'c' => array('sans-serif')),'Arsenal' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Arsenal SC' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Artifika' => array('v' => array('regular'), 'c' => array('serif')),'Arvo' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Arya' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Asap' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Asap Condensed' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Asar' => array('v' => array('regular'), 'c' => array('serif')),'Asset' => array('v' => array('regular'), 'c' => array('display')),'Assistant' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Astloch' => array('v' => array('regular','700'), 'c' => array('display')),'Asul' => array('v' => array('regular','700'), 'c' => array('serif')),'Athiti' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Atkinson Hyperlegible' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Atkinson Hyperlegible Mono' => array('v' => array('200','300','regular','500','600','700','800','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Atkinson Hyperlegible Next' => array('v' => array('200','300','regular','500','600','700','800','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Atma' => array('v' => array('300','regular','500','600','700'), 'c' => array('display')),'Atomic Age' => array('v' => array('regular'), 'c' => array('display')),'Aubrey' => array('v' => array('regular'), 'c' => array('display')),'Audiowide' => array('v' => array('regular'), 'c' => array('display')),'Autour One' => array('v' => array('regular'), 'c' => array('display')),'Average' => array('v' => array('regular'), 'c' => array('serif')),'Average Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Averia Gruesa Libre' => array('v' => array('regular'), 'c' => array('display')),'Averia Libre' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('display')),'Averia Sans Libre' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('display')),'Averia Serif Libre' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('display')),'Azeret Mono' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('monospace')),'B612' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'B612 Mono' => array('v' => array('regular','italic','700','700italic'), 'c' => array('monospace')),'BIZ UDGothic' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'BIZ UDMincho' => array('v' => array('regular','700'), 'c' => array('serif')),'BIZ UDPGothic' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'BIZ UDPMincho' => array('v' => array('regular','700'), 'c' => array('serif')),'Babylonica' => array('v' => array('regular'), 'c' => array('handwriting')),'Bacasime Antique' => array('v' => array('regular'), 'c' => array('serif')),'Bad Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Badeen Display' => array('v' => array('regular'), 'c' => array('display')),'Bagel Fat One' => array('v' => array('regular'), 'c' => array('display')),'Bahiana' => array('v' => array('regular'), 'c' => array('display')),'Bahianita' => array('v' => array('regular'), 'c' => array('display')),'Bai Jamjuree' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Bakbak One' => array('v' => array('regular'), 'c' => array('display')),'Ballet' => array('v' => array('regular'), 'c' => array('handwriting')),'Baloo 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Bhai 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Bhaijaan 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Bhaina 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Chettan 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Da 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Paaji 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Tamma 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Tammudu 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Baloo Thambi 2' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Balsamiq Sans' => array('v' => array('regular','italic','700','700italic'), 'c' => array('display')),'Balthazar' => array('v' => array('regular'), 'c' => array('serif')),'Bangers' => array('v' => array('regular'), 'c' => array('display')),'Barlow' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Barlow Condensed' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Barlow Semi Condensed' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Barriecito' => array('v' => array('regular'), 'c' => array('display')),'Barrio' => array('v' => array('regular'), 'c' => array('display')),'Basic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Baskervville' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Baskervville SC' => array('v' => array('regular'), 'c' => array('serif')),'Battambang' => array('v' => array('100','300','regular','700','900'), 'c' => array('display')),'Baumans' => array('v' => array('regular'), 'c' => array('display')),'Bayon' => array('v' => array('regular'), 'c' => array('sans-serif')),'Be Vietnam Pro' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Beau Rivage' => array('v' => array('regular'), 'c' => array('handwriting')),'Bebas Neue' => array('v' => array('regular'), 'c' => array('sans-serif')),'Beiruti' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Belanosima' => array('v' => array('regular','600','700'), 'c' => array('sans-serif')),'Belgrano' => array('v' => array('regular'), 'c' => array('serif')),'Bellefair' => array('v' => array('regular'), 'c' => array('serif')),'Belleza' => array('v' => array('regular'), 'c' => array('sans-serif')),'Bellota' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('display')),'Bellota Text' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('display')),'BenchNine' => array('v' => array('300','regular','700'), 'c' => array('sans-serif')),'Benne' => array('v' => array('regular'), 'c' => array('serif')),'Bentham' => array('v' => array('regular'), 'c' => array('serif')),'Berkshire Swash' => array('v' => array('regular'), 'c' => array('handwriting')),'Besley' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Beth Ellen' => array('v' => array('regular'), 'c' => array('handwriting')),'Bevan' => array('v' => array('regular','italic'), 'c' => array('serif')),'BhuTuka Expanded One' => array('v' => array('regular'), 'c' => array('serif')),'Big Shoulders' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Big Shoulders Inline' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Big Shoulders Stencil' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Bigelow Rules' => array('v' => array('regular'), 'c' => array('display')),'Bigshot One' => array('v' => array('regular'), 'c' => array('display')),'Bilbo' => array('v' => array('regular'), 'c' => array('handwriting')),'Bilbo Swash Caps' => array('v' => array('regular'), 'c' => array('handwriting')),'BioRhyme' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('serif')),'BioRhyme Expanded' => array('v' => array('200','300','regular','700','800'), 'c' => array('serif')),'Birthstone' => array('v' => array('regular'), 'c' => array('handwriting')),'Birthstone Bounce' => array('v' => array('regular','500'), 'c' => array('handwriting')),'Biryani' => array('v' => array('200','300','regular','600','700','800','900'), 'c' => array('sans-serif')),'Bitter' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Black And White Picture' => array('v' => array('regular'), 'c' => array('display')),'Black Han Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Black Ops One' => array('v' => array('regular'), 'c' => array('display')),'Blaka' => array('v' => array('regular'), 'c' => array('display')),'Blaka Hollow' => array('v' => array('regular'), 'c' => array('display')),'Blaka Ink' => array('v' => array('regular'), 'c' => array('display')),'Blinker' => array('v' => array('100','200','300','regular','600','700','800','900'), 'c' => array('sans-serif')),'Bodoni Moda' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Bodoni Moda SC' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Bokor' => array('v' => array('regular'), 'c' => array('display')),'Boldonse' => array('v' => array('regular'), 'c' => array('display')),'Bona Nova' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Bona Nova SC' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Bonbon' => array('v' => array('regular'), 'c' => array('handwriting')),'Bonheur Royale' => array('v' => array('regular'), 'c' => array('handwriting')),'Boogaloo' => array('v' => array('regular'), 'c' => array('display')),'Borel' => array('v' => array('regular'), 'c' => array('handwriting')),'Bowlby One' => array('v' => array('regular'), 'c' => array('display')),'Bowlby One SC' => array('v' => array('regular'), 'c' => array('display')),'Braah One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Brawler' => array('v' => array('regular','700'), 'c' => array('serif')),'Bree Serif' => array('v' => array('regular'), 'c' => array('serif')),'Bricolage Grotesque' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Bruno Ace' => array('v' => array('regular'), 'c' => array('display')),'Bruno Ace SC' => array('v' => array('regular'), 'c' => array('display')),'Brygada 1918' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Bubblegum Sans' => array('v' => array('regular'), 'c' => array('display')),'Bubbler One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Buda' => array('v' => array('300'), 'c' => array('display')),'Buenard' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Bungee' => array('v' => array('regular'), 'c' => array('display')),'Bungee Hairline' => array('v' => array('regular'), 'c' => array('display')),'Bungee Inline' => array('v' => array('regular'), 'c' => array('display')),'Bungee Outline' => array('v' => array('regular'), 'c' => array('display')),'Bungee Shade' => array('v' => array('regular'), 'c' => array('display')),'Bungee Spice' => array('v' => array('regular'), 'c' => array('display')),'Bungee Tint' => array('v' => array('regular'), 'c' => array('display')),'Butcherman' => array('v' => array('regular'), 'c' => array('display')),'Butterfly Kids' => array('v' => array('regular'), 'c' => array('handwriting')),'Bytesized' => array('v' => array('regular'), 'c' => array('sans-serif')),'Cabin' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Cabin Condensed' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Cabin Sketch' => array('v' => array('regular','700'), 'c' => array('display')),'Cactus Classical Serif' => array('v' => array('regular'), 'c' => array('serif')),'Caesar Dressing' => array('v' => array('regular'), 'c' => array('display')),'Cagliostro' => array('v' => array('regular'), 'c' => array('sans-serif')),'Cairo' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Cairo Play' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Cal Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Caladea' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Calistoga' => array('v' => array('regular'), 'c' => array('display')),'Calligraffitti' => array('v' => array('regular'), 'c' => array('handwriting')),'Cambay' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Cambo' => array('v' => array('regular'), 'c' => array('serif')),'Candal' => array('v' => array('regular'), 'c' => array('sans-serif')),'Cantarell' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Cantata One' => array('v' => array('regular'), 'c' => array('serif')),'Cantora One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Caprasimo' => array('v' => array('regular'), 'c' => array('display')),'Capriola' => array('v' => array('regular'), 'c' => array('sans-serif')),'Caramel' => array('v' => array('regular'), 'c' => array('handwriting')),'Carattere' => array('v' => array('regular'), 'c' => array('handwriting')),'Cardo' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Carlito' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Carme' => array('v' => array('regular'), 'c' => array('sans-serif')),'Carrois Gothic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Carrois Gothic SC' => array('v' => array('regular'), 'c' => array('sans-serif')),'Carter One' => array('v' => array('regular'), 'c' => array('display')),'Cascadia Code' => array('v' => array('200','300','regular','500','600','700','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Cascadia Mono' => array('v' => array('200','300','regular','500','600','700','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Castoro' => array('v' => array('regular','italic'), 'c' => array('serif')),'Castoro Titling' => array('v' => array('regular'), 'c' => array('display')),'Catamaran' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Caudex' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Caveat' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Caveat Brush' => array('v' => array('regular'), 'c' => array('handwriting')),'Cedarville Cursive' => array('v' => array('regular'), 'c' => array('handwriting')),'Ceviche One' => array('v' => array('regular'), 'c' => array('display')),'Chakra Petch' => array('v' => array('300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Changa' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Changa One' => array('v' => array('regular','italic'), 'c' => array('display')),'Chango' => array('v' => array('regular'), 'c' => array('display')),'Charis SIL' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Charm' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Charmonman' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Chathura' => array('v' => array('100','300','regular','700','800'), 'c' => array('sans-serif')),'Chau Philomene One' => array('v' => array('regular','italic'), 'c' => array('sans-serif')),'Chela One' => array('v' => array('regular'), 'c' => array('display')),'Chelsea Market' => array('v' => array('regular'), 'c' => array('display')),'Chenla' => array('v' => array('regular'), 'c' => array('display')),'Cherish' => array('v' => array('regular'), 'c' => array('handwriting')),'Cherry Bomb One' => array('v' => array('regular'), 'c' => array('display')),'Cherry Cream Soda' => array('v' => array('regular'), 'c' => array('display')),'Cherry Swash' => array('v' => array('regular','700'), 'c' => array('display')),'Chewy' => array('v' => array('regular'), 'c' => array('display')),'Chicle' => array('v' => array('regular'), 'c' => array('display')),'Chilanka' => array('v' => array('regular'), 'c' => array('handwriting')),'Chivo' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Chivo Mono' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('monospace')),'Chocolate Classical Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Chokokutai' => array('v' => array('regular'), 'c' => array('display')),'Chonburi' => array('v' => array('regular'), 'c' => array('display')),'Cinzel' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('serif')),'Cinzel Decorative' => array('v' => array('regular','700','900'), 'c' => array('display')),'Clicker Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Climate Crisis' => array('v' => array('regular'), 'c' => array('display')),'Coda' => array('v' => array('regular','800'), 'c' => array('display')),'Codystar' => array('v' => array('300','regular'), 'c' => array('display')),'Coiny' => array('v' => array('regular'), 'c' => array('display')),'Combo' => array('v' => array('regular'), 'c' => array('display')),'Comfortaa' => array('v' => array('300','regular','500','600','700'), 'c' => array('display')),'Comforter' => array('v' => array('regular'), 'c' => array('handwriting')),'Comforter Brush' => array('v' => array('regular'), 'c' => array('handwriting')),'Comic Neue' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('handwriting')),'Comic Relief' => array('v' => array('regular','700'), 'c' => array('display')),'Coming Soon' => array('v' => array('regular'), 'c' => array('handwriting')),'Comme' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Commissioner' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Concert One' => array('v' => array('regular'), 'c' => array('display')),'Condiment' => array('v' => array('regular'), 'c' => array('handwriting')),'Content' => array('v' => array('regular','700'), 'c' => array('display')),'Contrail One' => array('v' => array('regular'), 'c' => array('display')),'Convergence' => array('v' => array('regular'), 'c' => array('sans-serif')),'Cookie' => array('v' => array('regular'), 'c' => array('handwriting')),'Copse' => array('v' => array('regular'), 'c' => array('serif')),'Coral Pixels' => array('v' => array('regular'), 'c' => array('display')),'Corben' => array('v' => array('regular','700'), 'c' => array('display')),'Corinthia' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Cormorant' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Cormorant Garamond' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Cormorant Infant' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Cormorant SC' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Cormorant Unicase' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Cormorant Upright' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Courgette' => array('v' => array('regular'), 'c' => array('handwriting')),'Courier Prime' => array('v' => array('regular','italic','700','700italic'), 'c' => array('monospace')),'Cousine' => array('v' => array('regular','italic','700','700italic'), 'c' => array('monospace')),'Coustard' => array('v' => array('regular','900'), 'c' => array('serif')),'Covered By Your Grace' => array('v' => array('regular'), 'c' => array('handwriting')),'Crafty Girls' => array('v' => array('regular'), 'c' => array('handwriting')),'Creepster' => array('v' => array('regular'), 'c' => array('display')),'Crete Round' => array('v' => array('regular','italic'), 'c' => array('serif')),'Crimson Pro' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Crimson Text' => array('v' => array('regular','italic','600','600italic','700','700italic'), 'c' => array('serif')),'Croissant One' => array('v' => array('regular'), 'c' => array('display')),'Crushed' => array('v' => array('regular'), 'c' => array('display')),'Cuprum' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Cute Font' => array('v' => array('regular'), 'c' => array('display')),'Cutive' => array('v' => array('regular'), 'c' => array('serif')),'Cutive Mono' => array('v' => array('regular'), 'c' => array('monospace')),'DM Mono' => array('v' => array('300','300italic','regular','italic','500','500italic'), 'c' => array('monospace')),'DM Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'DM Serif Display' => array('v' => array('regular','italic'), 'c' => array('serif')),'DM Serif Text' => array('v' => array('regular','italic'), 'c' => array('serif')),'Dai Banna SIL' => array('v' => array('300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('serif')),'Damion' => array('v' => array('regular'), 'c' => array('handwriting')),'Dancing Script' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Danfo' => array('v' => array('regular'), 'c' => array('serif')),'Dangrek' => array('v' => array('regular'), 'c' => array('display')),'Darker Grotesque' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Darumadrop One' => array('v' => array('regular'), 'c' => array('display')),'David Libre' => array('v' => array('regular','500','700'), 'c' => array('serif')),'Dawning of a New Day' => array('v' => array('regular'), 'c' => array('handwriting')),'Days One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Dekko' => array('v' => array('regular'), 'c' => array('handwriting')),'Dela Gothic One' => array('v' => array('regular'), 'c' => array('display')),'Delicious Handrawn' => array('v' => array('regular'), 'c' => array('handwriting')),'Delius' => array('v' => array('regular'), 'c' => array('handwriting')),'Delius Swash Caps' => array('v' => array('regular'), 'c' => array('handwriting')),'Delius Unicase' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Della Respira' => array('v' => array('regular'), 'c' => array('serif')),'Denk One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Devonshire' => array('v' => array('regular'), 'c' => array('handwriting')),'Dhurjati' => array('v' => array('regular'), 'c' => array('sans-serif')),'Didact Gothic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Diphylleia' => array('v' => array('regular'), 'c' => array('serif')),'Diplomata' => array('v' => array('regular'), 'c' => array('display')),'Diplomata SC' => array('v' => array('regular'), 'c' => array('display')),'Do Hyeon' => array('v' => array('regular'), 'c' => array('sans-serif')),'Dokdo' => array('v' => array('regular'), 'c' => array('display')),'Domine' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Donegal One' => array('v' => array('regular'), 'c' => array('serif')),'Dongle' => array('v' => array('300','regular','700'), 'c' => array('sans-serif')),'Doppio One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Dorsa' => array('v' => array('regular'), 'c' => array('sans-serif')),'Dosis' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'DotGothic16' => array('v' => array('regular'), 'c' => array('sans-serif')),'Doto' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Dr Sugiyama' => array('v' => array('regular'), 'c' => array('handwriting')),'Duru Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'DynaPuff' => array('v' => array('regular','500','600','700'), 'c' => array('display')),'Dynalight' => array('v' => array('regular'), 'c' => array('display')),'EB Garamond' => array('v' => array('regular','500','600','700','800','italic','500italic','600italic','700italic','800italic'), 'c' => array('serif')),'Eagle Lake' => array('v' => array('regular'), 'c' => array('handwriting')),'East Sea Dokdo' => array('v' => array('regular'), 'c' => array('handwriting')),'Eater' => array('v' => array('regular'), 'c' => array('display')),'Economica' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Eczar' => array('v' => array('regular','500','600','700','800'), 'c' => array('serif')),'Edu AU VIC WA NT Arrows' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu AU VIC WA NT Dots' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu AU VIC WA NT Guides' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu AU VIC WA NT Hand' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu AU VIC WA NT Pre' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu NSW ACT Foundation' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu QLD Beginner' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu SA Beginner' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu TAS Beginner' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'Edu VIC WA NT Beginner' => array('v' => array('regular','500','600','700'), 'c' => array('handwriting')),'El Messiri' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Electrolize' => array('v' => array('regular'), 'c' => array('sans-serif')),'Elsie' => array('v' => array('regular','900'), 'c' => array('display')),'Elsie Swash Caps' => array('v' => array('regular','900'), 'c' => array('display')),'Emblema One' => array('v' => array('regular'), 'c' => array('display')),'Emilys Candy' => array('v' => array('regular'), 'c' => array('display')),'Encode Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Encode Sans Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Encode Sans Expanded' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Encode Sans SC' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Encode Sans Semi Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Encode Sans Semi Expanded' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Engagement' => array('v' => array('regular'), 'c' => array('handwriting')),'Englebert' => array('v' => array('regular'), 'c' => array('sans-serif')),'Enriqueta' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Ephesis' => array('v' => array('regular'), 'c' => array('handwriting')),'Epilogue' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Erica One' => array('v' => array('regular'), 'c' => array('display')),'Esteban' => array('v' => array('regular'), 'c' => array('serif')),'Estonia' => array('v' => array('regular'), 'c' => array('handwriting')),'Euphoria Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Ewert' => array('v' => array('regular'), 'c' => array('display')),'Exile' => array('v' => array('regular'), 'c' => array('display')),'Exo' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Exo 2' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Expletus Sans' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('display')),'Explora' => array('v' => array('regular'), 'c' => array('handwriting')),'Faculty Glyphic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Fahkwang' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Familjen Grotesk' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Fanwood Text' => array('v' => array('regular','italic'), 'c' => array('serif')),'Farro' => array('v' => array('300','regular','500','700'), 'c' => array('sans-serif')),'Farsan' => array('v' => array('regular'), 'c' => array('display')),'Fascinate' => array('v' => array('regular'), 'c' => array('display')),'Fascinate Inline' => array('v' => array('regular'), 'c' => array('display')),'Faster One' => array('v' => array('regular'), 'c' => array('display')),'Fasthand' => array('v' => array('regular'), 'c' => array('display')),'Fauna One' => array('v' => array('regular'), 'c' => array('serif')),'Faustina' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('serif')),'Federant' => array('v' => array('regular'), 'c' => array('display')),'Federo' => array('v' => array('regular'), 'c' => array('sans-serif')),'Felipa' => array('v' => array('regular'), 'c' => array('handwriting')),'Fenix' => array('v' => array('regular'), 'c' => array('serif')),'Festive' => array('v' => array('regular'), 'c' => array('handwriting')),'Figtree' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Finger Paint' => array('v' => array('regular'), 'c' => array('display')),'Finlandica' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Fira Code' => array('v' => array('300','regular','500','600','700'), 'c' => array('monospace')),'Fira Mono' => array('v' => array('regular','500','700'), 'c' => array('monospace')),'Fira Sans' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Fira Sans Condensed' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Fira Sans Extra Condensed' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Fjalla One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Fjord One' => array('v' => array('regular'), 'c' => array('serif')),'Flamenco' => array('v' => array('300','regular'), 'c' => array('display')),'Flavors' => array('v' => array('regular'), 'c' => array('display')),'Fleur De Leah' => array('v' => array('regular'), 'c' => array('handwriting')),'Flow Block' => array('v' => array('regular'), 'c' => array('display')),'Flow Circular' => array('v' => array('regular'), 'c' => array('display')),'Flow Rounded' => array('v' => array('regular'), 'c' => array('display')),'Foldit' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Fondamento' => array('v' => array('regular','italic'), 'c' => array('handwriting')),'Fontdiner Swanky' => array('v' => array('regular'), 'c' => array('display')),'Forum' => array('v' => array('regular'), 'c' => array('display')),'Fragment Mono' => array('v' => array('regular','italic'), 'c' => array('monospace')),'Francois One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Frank Ruhl Libre' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('serif')),'Fraunces' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Freckle Face' => array('v' => array('regular'), 'c' => array('display')),'Fredericka the Great' => array('v' => array('regular'), 'c' => array('display')),'Fredoka' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Freehand' => array('v' => array('regular'), 'c' => array('display')),'Freeman' => array('v' => array('regular'), 'c' => array('display')),'Fresca' => array('v' => array('regular'), 'c' => array('sans-serif')),'Frijole' => array('v' => array('regular'), 'c' => array('display')),'Fruktur' => array('v' => array('regular','italic'), 'c' => array('display')),'Fugaz One' => array('v' => array('regular'), 'c' => array('display')),'Fuggles' => array('v' => array('regular'), 'c' => array('handwriting')),'Funnel Display' => array('v' => array('300','regular','500','600','700','800'), 'c' => array('display')),'Funnel Sans' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Fustat' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Fuzzy Bubbles' => array('v' => array('regular','700'), 'c' => array('handwriting')),'GFS Didot' => array('v' => array('regular'), 'c' => array('serif')),'GFS Neohellenic' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Ga Maamli' => array('v' => array('regular'), 'c' => array('display')),'Gabarito' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('display')),'Gabriela' => array('v' => array('regular'), 'c' => array('serif')),'Gaegu' => array('v' => array('300','regular','700'), 'c' => array('handwriting')),'Gafata' => array('v' => array('regular'), 'c' => array('sans-serif')),'Gajraj One' => array('v' => array('regular'), 'c' => array('display')),'Galada' => array('v' => array('regular'), 'c' => array('display')),'Galdeano' => array('v' => array('regular'), 'c' => array('sans-serif')),'Galindo' => array('v' => array('regular'), 'c' => array('display')),'Gamja Flower' => array('v' => array('regular'), 'c' => array('handwriting')),'Gantari' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Gasoek One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Gayathri' => array('v' => array('100','regular','700'), 'c' => array('sans-serif')),'Geist' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Geist Mono' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('monospace')),'Gelasio' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Gemunu Libre' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Genos' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Gentium Book Plus' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Gentium Plus' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Geo' => array('v' => array('regular','italic'), 'c' => array('sans-serif')),'Geologica' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Georama' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Geostar' => array('v' => array('regular'), 'c' => array('display')),'Geostar Fill' => array('v' => array('regular'), 'c' => array('display')),'Germania One' => array('v' => array('regular'), 'c' => array('display')),'Gideon Roman' => array('v' => array('regular'), 'c' => array('display')),'Gidole' => array('v' => array('regular'), 'c' => array('sans-serif')),'Gidugu' => array('v' => array('regular'), 'c' => array('sans-serif')),'Gilda Display' => array('v' => array('regular'), 'c' => array('serif')),'Girassol' => array('v' => array('regular'), 'c' => array('display')),'Give You Glory' => array('v' => array('regular'), 'c' => array('handwriting')),'Glass Antiqua' => array('v' => array('regular'), 'c' => array('display')),'Glegoo' => array('v' => array('regular','700'), 'c' => array('serif')),'Gloock' => array('v' => array('regular'), 'c' => array('serif')),'Gloria Hallelujah' => array('v' => array('regular'), 'c' => array('handwriting')),'Glory' => array('v' => array('100','200','300','regular','500','600','700','800','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Gluten' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Goblin One' => array('v' => array('regular'), 'c' => array('display')),'Gochi Hand' => array('v' => array('regular'), 'c' => array('handwriting')),'Goldman' => array('v' => array('regular','700'), 'c' => array('display')),'Golos Text' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Gorditas' => array('v' => array('regular','700'), 'c' => array('display')),'Gothic A1' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Gotu' => array('v' => array('regular'), 'c' => array('sans-serif')),'Goudy Bookletter 1911' => array('v' => array('regular'), 'c' => array('serif')),'Gowun Batang' => array('v' => array('regular','700'), 'c' => array('serif')),'Gowun Dodum' => array('v' => array('regular'), 'c' => array('sans-serif')),'Graduate' => array('v' => array('regular'), 'c' => array('serif')),'Grand Hotel' => array('v' => array('regular'), 'c' => array('handwriting')),'Grandiflora One' => array('v' => array('regular'), 'c' => array('serif')),'Grandstander' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('display')),'Grape Nuts' => array('v' => array('regular'), 'c' => array('handwriting')),'Gravitas One' => array('v' => array('regular'), 'c' => array('display')),'Great Vibes' => array('v' => array('regular'), 'c' => array('handwriting')),'Grechen Fuemen' => array('v' => array('regular'), 'c' => array('handwriting')),'Grenze' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('serif')),'Grenze Gotisch' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Grey Qo' => array('v' => array('regular'), 'c' => array('handwriting')),'Griffy' => array('v' => array('regular'), 'c' => array('display')),'Gruppo' => array('v' => array('regular'), 'c' => array('sans-serif')),'Gudea' => array('v' => array('regular','italic','700'), 'c' => array('sans-serif')),'Gugi' => array('v' => array('regular'), 'c' => array('display')),'Gulzar' => array('v' => array('regular'), 'c' => array('serif')),'Gupter' => array('v' => array('regular','500','700'), 'c' => array('serif')),'Gurajada' => array('v' => array('regular'), 'c' => array('sans-serif')),'Gwendolyn' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Habibi' => array('v' => array('regular'), 'c' => array('serif')),'Hachi Maru Pop' => array('v' => array('regular'), 'c' => array('handwriting')),'Hahmlet' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Halant' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Hammersmith One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Hanalei' => array('v' => array('regular'), 'c' => array('display')),'Hanalei Fill' => array('v' => array('regular'), 'c' => array('display')),'Handjet' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Handlee' => array('v' => array('regular'), 'c' => array('handwriting')),'Hanken Grotesk' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Hanuman' => array('v' => array('100','300','regular','700','900'), 'c' => array('serif')),'Happy Monkey' => array('v' => array('regular'), 'c' => array('display')),'Harmattan' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Headland One' => array('v' => array('regular'), 'c' => array('serif')),'Hedvig Letters Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Hedvig Letters Serif' => array('v' => array('regular'), 'c' => array('serif')),'Heebo' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Henny Penny' => array('v' => array('regular'), 'c' => array('display')),'Hepta Slab' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Herr Von Muellerhoff' => array('v' => array('regular'), 'c' => array('handwriting')),'Hi Melody' => array('v' => array('regular'), 'c' => array('handwriting')),'Hina Mincho' => array('v' => array('regular'), 'c' => array('serif')),'Hind' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Hind Guntur' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Hind Madurai' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Hind Mysuru' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Hind Siliguri' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Hind Vadodara' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Holtwood One SC' => array('v' => array('regular'), 'c' => array('serif')),'Homemade Apple' => array('v' => array('regular'), 'c' => array('handwriting')),'Homenaje' => array('v' => array('regular'), 'c' => array('sans-serif')),'Honk' => array('v' => array('regular'), 'c' => array('display')),'Host Grotesk' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Hubballi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Hubot Sans' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Hurricane' => array('v' => array('regular'), 'c' => array('handwriting')),'IBM Plex Mono' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('monospace')),'IBM Plex Sans' => array('v' => array('100','200','300','regular','500','600','700','100italic','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'IBM Plex Sans Arabic' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Sans Condensed' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'IBM Plex Sans Devanagari' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Sans Hebrew' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Sans JP' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Sans KR' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Sans Thai' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Sans Thai Looped' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'IBM Plex Serif' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('serif')),'IM Fell DW Pica' => array('v' => array('regular','italic'), 'c' => array('serif')),'IM Fell DW Pica SC' => array('v' => array('regular'), 'c' => array('serif')),'IM Fell Double Pica' => array('v' => array('regular','italic'), 'c' => array('serif')),'IM Fell Double Pica SC' => array('v' => array('regular'), 'c' => array('serif')),'IM Fell English' => array('v' => array('regular','italic'), 'c' => array('serif')),'IM Fell English SC' => array('v' => array('regular'), 'c' => array('serif')),'IM Fell French Canon' => array('v' => array('regular','italic'), 'c' => array('serif')),'IM Fell French Canon SC' => array('v' => array('regular'), 'c' => array('serif')),'IM Fell Great Primer' => array('v' => array('regular','italic'), 'c' => array('serif')),'IM Fell Great Primer SC' => array('v' => array('regular'), 'c' => array('serif')),'Iansui' => array('v' => array('regular'), 'c' => array('handwriting')),'Ibarra Real Nova' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Iceberg' => array('v' => array('regular'), 'c' => array('display')),'Iceland' => array('v' => array('regular'), 'c' => array('display')),'Imbue' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Imperial Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Imprima' => array('v' => array('regular'), 'c' => array('sans-serif')),'Inclusive Sans' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Inconsolata' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('monospace')),'Inder' => array('v' => array('regular'), 'c' => array('sans-serif')),'Indie Flower' => array('v' => array('regular'), 'c' => array('handwriting')),'Ingrid Darling' => array('v' => array('regular'), 'c' => array('handwriting')),'Inika' => array('v' => array('regular','700'), 'c' => array('serif')),'Inknut Antiqua' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('serif')),'Inria Sans' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('sans-serif')),'Inria Serif' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('serif')),'Inspiration' => array('v' => array('regular'), 'c' => array('handwriting')),'Instrument Sans' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Instrument Serif' => array('v' => array('regular','italic'), 'c' => array('serif')),'Inter' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Inter Tight' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Irish Grover' => array('v' => array('regular'), 'c' => array('display')),'Island Moments' => array('v' => array('regular'), 'c' => array('handwriting')),'Istok Web' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Italiana' => array('v' => array('regular'), 'c' => array('sans-serif')),'Italianno' => array('v' => array('regular'), 'c' => array('handwriting')),'Itim' => array('v' => array('regular'), 'c' => array('handwriting')),'Jacquard 12' => array('v' => array('regular'), 'c' => array('display')),'Jacquard 12 Charted' => array('v' => array('regular'), 'c' => array('display')),'Jacquard 24' => array('v' => array('regular'), 'c' => array('display')),'Jacquard 24 Charted' => array('v' => array('regular'), 'c' => array('display')),'Jacquarda Bastarda 9' => array('v' => array('regular'), 'c' => array('display')),'Jacquarda Bastarda 9 Charted' => array('v' => array('regular'), 'c' => array('display')),'Jacques Francois' => array('v' => array('regular'), 'c' => array('serif')),'Jacques Francois Shadow' => array('v' => array('regular'), 'c' => array('display')),'Jaini' => array('v' => array('regular'), 'c' => array('display')),'Jaini Purva' => array('v' => array('regular'), 'c' => array('display')),'Jaldi' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Jaro' => array('v' => array('regular'), 'c' => array('sans-serif')),'Jersey 10' => array('v' => array('regular'), 'c' => array('display')),'Jersey 10 Charted' => array('v' => array('regular'), 'c' => array('display')),'Jersey 15' => array('v' => array('regular'), 'c' => array('display')),'Jersey 15 Charted' => array('v' => array('regular'), 'c' => array('display')),'Jersey 20' => array('v' => array('regular'), 'c' => array('display')),'Jersey 20 Charted' => array('v' => array('regular'), 'c' => array('display')),'Jersey 25' => array('v' => array('regular'), 'c' => array('display')),'Jersey 25 Charted' => array('v' => array('regular'), 'c' => array('display')),'JetBrains Mono' => array('v' => array('100','200','300','regular','500','600','700','800','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('monospace')),'Jim Nightshade' => array('v' => array('regular'), 'c' => array('handwriting')),'Joan' => array('v' => array('regular'), 'c' => array('serif')),'Jockey One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Jolly Lodger' => array('v' => array('regular'), 'c' => array('display')),'Jomhuria' => array('v' => array('regular'), 'c' => array('display')),'Jomolhari' => array('v' => array('regular'), 'c' => array('serif')),'Josefin Sans' => array('v' => array('100','200','300','regular','500','600','700','100italic','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Josefin Slab' => array('v' => array('100','200','300','regular','500','600','700','100italic','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Jost' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Joti One' => array('v' => array('regular'), 'c' => array('display')),'Jua' => array('v' => array('regular'), 'c' => array('sans-serif')),'Judson' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Julee' => array('v' => array('regular'), 'c' => array('handwriting')),'Julius Sans One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Junge' => array('v' => array('regular'), 'c' => array('serif')),'Jura' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Just Another Hand' => array('v' => array('regular'), 'c' => array('handwriting')),'Just Me Again Down Here' => array('v' => array('regular'), 'c' => array('handwriting')),'K2D' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic'), 'c' => array('sans-serif')),'Kablammo' => array('v' => array('regular'), 'c' => array('display')),'Kadwa' => array('v' => array('regular','700'), 'c' => array('serif')),'Kaisei Decol' => array('v' => array('regular','500','700'), 'c' => array('serif')),'Kaisei HarunoUmi' => array('v' => array('regular','500','700'), 'c' => array('serif')),'Kaisei Opti' => array('v' => array('regular','500','700'), 'c' => array('serif')),'Kaisei Tokumin' => array('v' => array('regular','500','700','800'), 'c' => array('serif')),'Kalam' => array('v' => array('300','regular','700'), 'c' => array('handwriting')),'Kalnia' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('serif')),'Kalnia Glaze' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('display')),'Kameron' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Kanchenjunga' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Kanit' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Kantumruy Pro' => array('v' => array('100','200','300','regular','500','600','700','100italic','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Kapakana' => array('v' => array('300','regular'), 'c' => array('handwriting')),'Karantina' => array('v' => array('300','regular','700'), 'c' => array('display')),'Karla' => array('v' => array('200','300','regular','500','600','700','800','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Karla Tamil Inclined' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Karla Tamil Upright' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Karma' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Katibeh' => array('v' => array('regular'), 'c' => array('display')),'Kaushan Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Kavivanar' => array('v' => array('regular'), 'c' => array('handwriting')),'Kavoon' => array('v' => array('regular'), 'c' => array('display')),'Kay Pho Du' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Kdam Thmor Pro' => array('v' => array('regular'), 'c' => array('sans-serif')),'Keania One' => array('v' => array('regular'), 'c' => array('display')),'Kelly Slab' => array('v' => array('regular'), 'c' => array('display')),'Kenia' => array('v' => array('regular'), 'c' => array('display')),'Khand' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Khmer' => array('v' => array('regular'), 'c' => array('sans-serif')),'Khula' => array('v' => array('300','regular','600','700','800'), 'c' => array('sans-serif')),'Kings' => array('v' => array('regular'), 'c' => array('handwriting')),'Kirang Haerang' => array('v' => array('regular'), 'c' => array('display')),'Kite One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Kiwi Maru' => array('v' => array('300','regular','500'), 'c' => array('serif')),'Klee One' => array('v' => array('regular','600'), 'c' => array('handwriting')),'Knewave' => array('v' => array('regular'), 'c' => array('display')),'KoHo' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Kodchasan' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Kode Mono' => array('v' => array('regular','500','600','700'), 'c' => array('monospace')),'Koh Santepheap' => array('v' => array('100','300','regular','700','900'), 'c' => array('serif')),'Kolker Brush' => array('v' => array('regular'), 'c' => array('handwriting')),'Konkhmer Sleokchher' => array('v' => array('regular'), 'c' => array('display')),'Kosugi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Kosugi Maru' => array('v' => array('regular'), 'c' => array('sans-serif')),'Kotta One' => array('v' => array('regular'), 'c' => array('serif')),'Koulen' => array('v' => array('regular'), 'c' => array('display')),'Kranky' => array('v' => array('regular'), 'c' => array('display')),'Kreon' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Kristi' => array('v' => array('regular'), 'c' => array('handwriting')),'Krona One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Krub' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Kufam' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Kulim Park' => array('v' => array('200','200italic','300','300italic','regular','italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Kumar One' => array('v' => array('regular'), 'c' => array('display')),'Kumar One Outline' => array('v' => array('regular'), 'c' => array('display')),'Kumbh Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Kurale' => array('v' => array('regular'), 'c' => array('serif')),'LXGW WenKai Mono TC' => array('v' => array('300','regular','700'), 'c' => array('monospace')),'LXGW WenKai TC' => array('v' => array('300','regular','700'), 'c' => array('handwriting')),'La Belle Aurore' => array('v' => array('regular'), 'c' => array('handwriting')),'Labrada' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Lacquer' => array('v' => array('regular'), 'c' => array('display')),'Laila' => array('v' => array('300','regular','500','600','700'), 'c' => array('serif')),'Lakki Reddy' => array('v' => array('regular'), 'c' => array('handwriting')),'Lalezar' => array('v' => array('regular'), 'c' => array('sans-serif')),'Lancelot' => array('v' => array('regular'), 'c' => array('display')),'Langar' => array('v' => array('regular'), 'c' => array('display')),'Lateef' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('serif')),'Lato' => array('v' => array('100','100italic','300','300italic','regular','italic','700','700italic','900','900italic'), 'c' => array('sans-serif')),'Lavishly Yours' => array('v' => array('regular'), 'c' => array('handwriting')),'League Gothic' => array('v' => array('regular'), 'c' => array('sans-serif')),'League Script' => array('v' => array('regular'), 'c' => array('handwriting')),'League Spartan' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Leckerli One' => array('v' => array('regular'), 'c' => array('handwriting')),'Ledger' => array('v' => array('regular'), 'c' => array('serif')),'Lekton' => array('v' => array('regular','italic','700'), 'c' => array('monospace')),'Lemon' => array('v' => array('regular'), 'c' => array('display')),'Lemonada' => array('v' => array('300','regular','500','600','700'), 'c' => array('display')),'Lexend' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Deca' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Exa' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Giga' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Mega' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Peta' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Tera' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Lexend Zetta' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Libre Barcode 128' => array('v' => array('regular'), 'c' => array('display')),'Libre Barcode 128 Text' => array('v' => array('regular'), 'c' => array('display')),'Libre Barcode 39' => array('v' => array('regular'), 'c' => array('display')),'Libre Barcode 39 Extended' => array('v' => array('regular'), 'c' => array('display')),'Libre Barcode 39 Extended Text' => array('v' => array('regular'), 'c' => array('display')),'Libre Barcode 39 Text' => array('v' => array('regular'), 'c' => array('display')),'Libre Barcode EAN13 Text' => array('v' => array('regular'), 'c' => array('display')),'Libre Baskerville' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Libre Bodoni' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Libre Caslon Display' => array('v' => array('regular'), 'c' => array('serif')),'Libre Caslon Text' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Libre Franklin' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Licorice' => array('v' => array('regular'), 'c' => array('handwriting')),'Life Savers' => array('v' => array('regular','700','800'), 'c' => array('display')),'Lilita One' => array('v' => array('regular'), 'c' => array('display')),'Lily Script One' => array('v' => array('regular'), 'c' => array('display')),'Limelight' => array('v' => array('regular'), 'c' => array('display')),'Linden Hill' => array('v' => array('regular','italic'), 'c' => array('serif')),'Linefont' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Lisu Bosa' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('serif')),'Liter' => array('v' => array('regular'), 'c' => array('sans-serif')),'Literata' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Liu Jian Mao Cao' => array('v' => array('regular'), 'c' => array('handwriting')),'Livvic' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','900','900italic'), 'c' => array('sans-serif')),'Lobster' => array('v' => array('regular'), 'c' => array('display')),'Lobster Two' => array('v' => array('regular','italic','700','700italic'), 'c' => array('display')),'Londrina Outline' => array('v' => array('regular'), 'c' => array('display')),'Londrina Shadow' => array('v' => array('regular'), 'c' => array('display')),'Londrina Sketch' => array('v' => array('regular'), 'c' => array('display')),'Londrina Solid' => array('v' => array('100','300','regular','900'), 'c' => array('display')),'Long Cang' => array('v' => array('regular'), 'c' => array('handwriting')),'Lora' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Love Light' => array('v' => array('regular'), 'c' => array('handwriting')),'Love Ya Like A Sister' => array('v' => array('regular'), 'c' => array('display')),'Loved by the King' => array('v' => array('regular'), 'c' => array('handwriting')),'Lovers Quarrel' => array('v' => array('regular'), 'c' => array('handwriting')),'Luckiest Guy' => array('v' => array('regular'), 'c' => array('display')),'Lugrasimo' => array('v' => array('regular'), 'c' => array('handwriting')),'Lumanosimo' => array('v' => array('regular'), 'c' => array('handwriting')),'Lunasima' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Lusitana' => array('v' => array('regular','700'), 'c' => array('serif')),'Lustria' => array('v' => array('regular'), 'c' => array('serif')),'Luxurious Roman' => array('v' => array('regular'), 'c' => array('display')),'Luxurious Script' => array('v' => array('regular'), 'c' => array('handwriting')),'M PLUS 1' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'M PLUS 1 Code' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('monospace')),'M PLUS 1p' => array('v' => array('100','300','regular','500','700','800','900'), 'c' => array('sans-serif')),'M PLUS 2' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'M PLUS Code Latin' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'M PLUS Rounded 1c' => array('v' => array('100','300','regular','500','700','800','900'), 'c' => array('sans-serif')),'Ma Shan Zheng' => array('v' => array('regular'), 'c' => array('handwriting')),'Macondo' => array('v' => array('regular'), 'c' => array('display')),'Macondo Swash Caps' => array('v' => array('regular'), 'c' => array('display')),'Mada' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Madimi One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Magra' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Maiden Orange' => array('v' => array('regular'), 'c' => array('serif')),'Maitree' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('serif')),'Major Mono Display' => array('v' => array('regular'), 'c' => array('monospace')),'Mako' => array('v' => array('regular'), 'c' => array('sans-serif')),'Mali' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('handwriting')),'Mallanna' => array('v' => array('regular'), 'c' => array('sans-serif')),'Maname' => array('v' => array('regular'), 'c' => array('serif')),'Mandali' => array('v' => array('regular'), 'c' => array('sans-serif')),'Manjari' => array('v' => array('100','regular','700'), 'c' => array('sans-serif')),'Manrope' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Mansalva' => array('v' => array('regular'), 'c' => array('handwriting')),'Manuale' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('serif')),'Marcellus' => array('v' => array('regular'), 'c' => array('serif')),'Marcellus SC' => array('v' => array('regular'), 'c' => array('serif')),'Marck Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Margarine' => array('v' => array('regular'), 'c' => array('display')),'Marhey' => array('v' => array('300','regular','500','600','700'), 'c' => array('display')),'Markazi Text' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Marko One' => array('v' => array('regular'), 'c' => array('serif')),'Marmelad' => array('v' => array('regular'), 'c' => array('sans-serif')),'Martel' => array('v' => array('200','300','regular','600','700','800','900'), 'c' => array('serif')),'Martel Sans' => array('v' => array('200','300','regular','600','700','800','900'), 'c' => array('sans-serif')),'Martian Mono' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('monospace')),'Marvel' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Mate' => array('v' => array('regular','italic'), 'c' => array('serif')),'Mate SC' => array('v' => array('regular'), 'c' => array('serif')),'Matemasie' => array('v' => array('regular'), 'c' => array('sans-serif')),'Material Icons' => array('v' => array('regular'), 'c' => array('monospace')),'Material Icons Outlined' => array('v' => array('regular'), 'c' => array('monospace')),'Material Icons Round' => array('v' => array('regular'), 'c' => array('monospace')),'Material Icons Sharp' => array('v' => array('regular'), 'c' => array('monospace')),'Material Icons Two Tone' => array('v' => array('regular'), 'c' => array('monospace')),'Material Symbols' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('monospace')),'Material Symbols Outlined' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('monospace')),'Material Symbols Rounded' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('monospace')),'Material Symbols Sharp' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('monospace')),'Maven Pro' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('sans-serif')),'McLaren' => array('v' => array('regular'), 'c' => array('display')),'Mea Culpa' => array('v' => array('regular'), 'c' => array('handwriting')),'Meddon' => array('v' => array('regular'), 'c' => array('handwriting')),'MedievalSharp' => array('v' => array('regular'), 'c' => array('display')),'Medula One' => array('v' => array('regular'), 'c' => array('display')),'Meera Inimai' => array('v' => array('regular'), 'c' => array('sans-serif')),'Megrim' => array('v' => array('regular'), 'c' => array('display')),'Meie Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Meow Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Merienda' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('handwriting')),'Merriweather' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Merriweather Sans' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Metal' => array('v' => array('regular'), 'c' => array('display')),'Metal Mania' => array('v' => array('regular'), 'c' => array('display')),'Metamorphous' => array('v' => array('regular'), 'c' => array('display')),'Metrophobic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Michroma' => array('v' => array('regular'), 'c' => array('sans-serif')),'Micro 5' => array('v' => array('regular'), 'c' => array('display')),'Micro 5 Charted' => array('v' => array('regular'), 'c' => array('display')),'Milonga' => array('v' => array('regular'), 'c' => array('display')),'Miltonian' => array('v' => array('regular'), 'c' => array('display')),'Miltonian Tattoo' => array('v' => array('regular'), 'c' => array('display')),'Mina' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Mingzat' => array('v' => array('regular'), 'c' => array('sans-serif')),'Miniver' => array('v' => array('regular'), 'c' => array('display')),'Miriam Libre' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Mirza' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Miss Fajardose' => array('v' => array('regular'), 'c' => array('handwriting')),'Mitr' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Mochiy Pop One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Mochiy Pop P One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Modak' => array('v' => array('regular'), 'c' => array('display')),'Modern Antiqua' => array('v' => array('regular'), 'c' => array('display')),'Moderustic' => array('v' => array('300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Mogra' => array('v' => array('regular'), 'c' => array('display')),'Mohave' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Moirai One' => array('v' => array('regular'), 'c' => array('display')),'Molengo' => array('v' => array('regular'), 'c' => array('sans-serif')),'Molle' => array('v' => array('italic'), 'c' => array('handwriting')),'Mona Sans' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Monda' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Monofett' => array('v' => array('regular'), 'c' => array('monospace')),'Monomakh' => array('v' => array('regular'), 'c' => array('display')),'Monomaniac One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Monoton' => array('v' => array('regular'), 'c' => array('display')),'Monsieur La Doulaise' => array('v' => array('regular'), 'c' => array('handwriting')),'Montaga' => array('v' => array('regular'), 'c' => array('serif')),'Montagu Slab' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('serif')),'MonteCarlo' => array('v' => array('regular'), 'c' => array('handwriting')),'Montez' => array('v' => array('regular'), 'c' => array('handwriting')),'Montserrat' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Montserrat Alternates' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Montserrat Underline' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Moo Lah Lah' => array('v' => array('regular'), 'c' => array('display')),'Mooli' => array('v' => array('regular'), 'c' => array('sans-serif')),'Moon Dance' => array('v' => array('regular'), 'c' => array('handwriting')),'Moul' => array('v' => array('regular'), 'c' => array('display')),'Moulpali' => array('v' => array('regular'), 'c' => array('sans-serif')),'Mountains of Christmas' => array('v' => array('regular','700'), 'c' => array('display')),'Mouse Memoirs' => array('v' => array('regular'), 'c' => array('sans-serif')),'Mr Bedfort' => array('v' => array('regular'), 'c' => array('handwriting')),'Mr Dafoe' => array('v' => array('regular'), 'c' => array('handwriting')),'Mr De Haviland' => array('v' => array('regular'), 'c' => array('handwriting')),'Mrs Saint Delafield' => array('v' => array('regular'), 'c' => array('handwriting')),'Mrs Sheppards' => array('v' => array('regular'), 'c' => array('handwriting')),'Ms Madi' => array('v' => array('regular'), 'c' => array('handwriting')),'Mukta' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Mukta Mahee' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Mukta Malar' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Mukta Vaani' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Mulish' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Murecho' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'MuseoModerno' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('display')),'My Soul' => array('v' => array('regular'), 'c' => array('handwriting')),'Mynerve' => array('v' => array('regular'), 'c' => array('handwriting')),'Mystery Quest' => array('v' => array('regular'), 'c' => array('display')),'NTR' => array('v' => array('regular'), 'c' => array('sans-serif')),'Nabla' => array('v' => array('regular'), 'c' => array('display')),'Namdhinggo' => array('v' => array('regular','500','600','700','800'), 'c' => array('serif')),'Nanum Brush Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Nanum Gothic' => array('v' => array('regular','700','800'), 'c' => array('sans-serif')),'Nanum Gothic Coding' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Nanum Myeongjo' => array('v' => array('regular','700','800'), 'c' => array('serif')),'Nanum Pen Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Narnoor' => array('v' => array('regular','500','600','700','800'), 'c' => array('sans-serif')),'National Park' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Neonderthaw' => array('v' => array('regular'), 'c' => array('handwriting')),'Nerko One' => array('v' => array('regular'), 'c' => array('handwriting')),'Neucha' => array('v' => array('regular'), 'c' => array('handwriting')),'Neuton' => array('v' => array('200','300','regular','italic','700','800'), 'c' => array('serif')),'New Amsterdam' => array('v' => array('regular'), 'c' => array('sans-serif')),'New Rocker' => array('v' => array('regular'), 'c' => array('display')),'New Tegomin' => array('v' => array('regular'), 'c' => array('serif')),'News Cycle' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Newsreader' => array('v' => array('200','300','regular','500','600','700','800','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('serif')),'Niconne' => array('v' => array('regular'), 'c' => array('handwriting')),'Niramit' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('sans-serif')),'Nixie One' => array('v' => array('regular'), 'c' => array('display')),'Nobile' => array('v' => array('regular','italic','500','500italic','700','700italic'), 'c' => array('sans-serif')),'Nokora' => array('v' => array('100','300','regular','700','900'), 'c' => array('sans-serif')),'Norican' => array('v' => array('regular'), 'c' => array('handwriting')),'Nosifer' => array('v' => array('regular'), 'c' => array('display')),'Notable' => array('v' => array('regular'), 'c' => array('sans-serif')),'Nothing You Could Do' => array('v' => array('regular'), 'c' => array('handwriting')),'Noticia Text' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Noto Color Emoji' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Emoji' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Noto Kufi Arabic' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Music' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Naskh Arabic' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Nastaliq Urdu' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Rashi Hebrew' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Noto Sans Adlam' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Adlam Unjoined' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Anatolian Hieroglyphs' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Arabic' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Armenian' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Avestan' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Balinese' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Bamum' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Bassa Vah' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Batak' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Bengali' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Bhaiksuki' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Brahmi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Buginese' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Buhid' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Canadian Aboriginal' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Carian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Caucasian Albanian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Chakma' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Cham' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Cherokee' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Chorasmian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Coptic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Cuneiform' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Cypriot' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Cypro Minoan' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Deseret' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Devanagari' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Display' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Noto Sans Duployan' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Noto Sans Egyptian Hieroglyphs' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Elbasan' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Elymaic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Ethiopic' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Georgian' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Glagolitic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Gothic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Grantha' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Gujarati' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Gunjala Gondi' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Gurmukhi' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans HK' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Hanifi Rohingya' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Hanunoo' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Hatran' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Hebrew' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Imperial Aramaic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Indic Siyaq Numbers' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Inscriptional Pahlavi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Inscriptional Parthian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans JP' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Javanese' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans KR' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Kaithi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Kannada' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Kawi' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Kayah Li' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Kharoshthi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Khmer' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Khojki' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Khudawadi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Lao' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Lao Looped' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Lepcha' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Limbu' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Linear A' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Linear B' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Lisu' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Lycian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Lydian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Mahajani' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Malayalam' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Mandaic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Manichaean' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Marchen' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Masaram Gondi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Math' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Mayan Numerals' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Medefaidrin' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Meetei Mayek' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Mende Kikakui' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Meroitic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Miao' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Modi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Mongolian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Mono' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Mro' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Multani' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Myanmar' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans NKo' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans NKo Unjoined' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Nabataean' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Nag Mundari' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Nandinagari' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans New Tai Lue' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Newa' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Nushu' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Ogham' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Ol Chiki' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Old Hungarian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old Italic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old North Arabian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old Permic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old Persian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old Sogdian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old South Arabian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Old Turkic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Oriya' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Osage' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Osmanya' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Pahawh Hmong' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Palmyrene' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Pau Cin Hau' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans PhagsPa' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Phoenician' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Psalter Pahlavi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Rejang' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Runic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans SC' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Samaritan' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Saurashtra' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Sharada' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Shavian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Siddham' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans SignWriting' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Sinhala' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Sogdian' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Sora Sompeng' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Soyombo' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Sundanese' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Syloti Nagri' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Symbols' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Symbols 2' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Syriac' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Syriac Eastern' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans TC' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Tagalog' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Tagbanwa' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Tai Le' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Tai Tham' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Tai Viet' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Takri' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Tamil' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Tamil Supplement' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Tangsa' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Telugu' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Thaana' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Thai' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Thai Looped' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Noto Sans Tifinagh' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Tirhuta' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Ugaritic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Vai' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Vithkuqi' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Noto Sans Wancho' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Warang Citi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Yi' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Sans Zanabazar Square' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Serif' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Noto Serif Ahom' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Armenian' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Balinese' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Bengali' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Devanagari' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Display' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Noto Serif Dives Akuru' => array('v' => array('regular'), 'c' => array('sans-serif')),'Noto Serif Dogra' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Ethiopic' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Georgian' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Grantha' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Gujarati' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Gurmukhi' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif HK' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Hebrew' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Hentaigana' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif JP' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif KR' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Kannada' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Khitan Small Script' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Khmer' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Khojki' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Serif Lao' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Makasar' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Malayalam' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Myanmar' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif NP Hmong' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Serif Old Uyghur' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Oriya' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Serif Ottoman Siyaq' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif SC' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Sinhala' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif TC' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Tamil' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Noto Serif Tangut' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Telugu' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Thai' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Tibetan' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Noto Serif Todhri' => array('v' => array('regular'), 'c' => array('serif')),'Noto Serif Toto' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Serif Vithkuqi' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Serif Yezidi' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Noto Traditional Nushu' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Noto Znamenny Musical Notation' => array('v' => array('regular'), 'c' => array('sans-serif')),'Nova Cut' => array('v' => array('regular'), 'c' => array('display')),'Nova Flat' => array('v' => array('regular'), 'c' => array('display')),'Nova Mono' => array('v' => array('regular'), 'c' => array('monospace')),'Nova Oval' => array('v' => array('regular'), 'c' => array('display')),'Nova Round' => array('v' => array('regular'), 'c' => array('display')),'Nova Script' => array('v' => array('regular'), 'c' => array('display')),'Nova Slim' => array('v' => array('regular'), 'c' => array('display')),'Nova Square' => array('v' => array('regular'), 'c' => array('display')),'Numans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Nunito' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Nunito Sans' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Nuosu SIL' => array('v' => array('regular'), 'c' => array('sans-serif')),'Odibee Sans' => array('v' => array('regular'), 'c' => array('display')),'Odor Mean Chey' => array('v' => array('regular'), 'c' => array('serif')),'Offside' => array('v' => array('regular'), 'c' => array('display')),'Oi' => array('v' => array('regular'), 'c' => array('display')),'Ojuju' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Old Standard TT' => array('v' => array('regular','italic','700'), 'c' => array('serif')),'Oldenburg' => array('v' => array('regular'), 'c' => array('display')),'Ole' => array('v' => array('regular'), 'c' => array('handwriting')),'Oleo Script' => array('v' => array('regular','700'), 'c' => array('display')),'Oleo Script Swash Caps' => array('v' => array('regular','700'), 'c' => array('display')),'Onest' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Oooh Baby' => array('v' => array('regular'), 'c' => array('handwriting')),'Open Sans' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Oranienbaum' => array('v' => array('regular'), 'c' => array('serif')),'Orbit' => array('v' => array('regular'), 'c' => array('sans-serif')),'Orbitron' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Oregano' => array('v' => array('regular','italic'), 'c' => array('display')),'Orelega One' => array('v' => array('regular'), 'c' => array('display')),'Orienta' => array('v' => array('regular'), 'c' => array('sans-serif')),'Original Surfer' => array('v' => array('regular'), 'c' => array('display')),'Oswald' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Outfit' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Over the Rainbow' => array('v' => array('regular'), 'c' => array('handwriting')),'Overlock' => array('v' => array('regular','italic','700','700italic','900','900italic'), 'c' => array('display')),'Overlock SC' => array('v' => array('regular'), 'c' => array('display')),'Overpass' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Overpass Mono' => array('v' => array('300','regular','500','600','700'), 'c' => array('monospace')),'Ovo' => array('v' => array('regular'), 'c' => array('serif')),'Oxanium' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('display')),'Oxygen' => array('v' => array('300','regular','700'), 'c' => array('sans-serif')),'Oxygen Mono' => array('v' => array('regular'), 'c' => array('monospace')),'PT Mono' => array('v' => array('regular'), 'c' => array('monospace')),'PT Sans' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'PT Sans Caption' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'PT Sans Narrow' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'PT Serif' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'PT Serif Caption' => array('v' => array('regular','italic'), 'c' => array('serif')),'Pacifico' => array('v' => array('regular'), 'c' => array('handwriting')),'Padauk' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Padyakke Expanded One' => array('v' => array('regular'), 'c' => array('serif')),'Palanquin' => array('v' => array('100','200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Palanquin Dark' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Palette Mosaic' => array('v' => array('regular'), 'c' => array('display')),'Pangolin' => array('v' => array('regular'), 'c' => array('handwriting')),'Paprika' => array('v' => array('regular'), 'c' => array('display')),'Parisienne' => array('v' => array('regular'), 'c' => array('handwriting')),'Parkinsans' => array('v' => array('300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Passero One' => array('v' => array('regular'), 'c' => array('display')),'Passion One' => array('v' => array('regular','700','900'), 'c' => array('display')),'Passions Conflict' => array('v' => array('regular'), 'c' => array('handwriting')),'Pathway Extreme' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Pathway Gothic One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Patrick Hand' => array('v' => array('regular'), 'c' => array('handwriting')),'Patrick Hand SC' => array('v' => array('regular'), 'c' => array('handwriting')),'Pattaya' => array('v' => array('regular'), 'c' => array('sans-serif')),'Patua One' => array('v' => array('regular'), 'c' => array('display')),'Pavanam' => array('v' => array('regular'), 'c' => array('sans-serif')),'Paytone One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Peddana' => array('v' => array('regular'), 'c' => array('serif')),'Peralta' => array('v' => array('regular'), 'c' => array('serif')),'Permanent Marker' => array('v' => array('regular'), 'c' => array('handwriting')),'Petemoss' => array('v' => array('regular'), 'c' => array('handwriting')),'Petit Formal Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Petrona' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Phetsarath' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Philosopher' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Phudu' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('display')),'Piazzolla' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Piedra' => array('v' => array('regular'), 'c' => array('display')),'Pinyon Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Pirata One' => array('v' => array('regular'), 'c' => array('display')),'Pixelify Sans' => array('v' => array('regular','500','600','700'), 'c' => array('display')),'Plaster' => array('v' => array('regular'), 'c' => array('display')),'Platypi' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('serif')),'Play' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Playball' => array('v' => array('regular'), 'c' => array('display')),'Playfair' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Playfair Display' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Playfair Display SC' => array('v' => array('regular','italic','700','700italic','900','900italic'), 'c' => array('serif')),'Playpen Sans' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('handwriting')),'Playpen Sans Arabic' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('handwriting')),'Playpen Sans Deva' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('handwriting')),'Playpen Sans Hebrew' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('handwriting')),'Playpen Sans Thai' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('handwriting')),'Playwrite AR' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite AR Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite AT' => array('v' => array('100','200','300','regular','100italic','200italic','300italic','italic'), 'c' => array('handwriting')),'Playwrite AT Guides' => array('v' => array('regular','italic'), 'c' => array('handwriting')),'Playwrite AU NSW' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite AU NSW Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite AU QLD' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite AU QLD Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite AU SA' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite AU SA Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite AU TAS' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite AU TAS Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite AU VIC' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite AU VIC Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite BE VLG' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite BE VLG Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite BE WAL' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite BE WAL Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite BR' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite BR Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite CA' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite CA Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite CL' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite CL Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite CO' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite CO Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite CU' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite CU Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite CZ' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite CZ Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite DE Grund' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite DE Grund Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite DE LA' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite DE LA Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite DE SAS' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite DE SAS Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite DE VA' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite DE VA Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite DK Loopet' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite DK Loopet Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite DK Uloopet' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite DK Uloopet Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite ES' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite ES Deco' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite ES Deco Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite ES Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite FR Moderne' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite FR Moderne Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite FR Trad' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite FR Trad Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite GB J' => array('v' => array('100','200','300','regular','100italic','200italic','300italic','italic'), 'c' => array('handwriting')),'Playwrite GB J Guides' => array('v' => array('regular','italic'), 'c' => array('handwriting')),'Playwrite GB S' => array('v' => array('100','200','300','regular','100italic','200italic','300italic','italic'), 'c' => array('handwriting')),'Playwrite GB S Guides' => array('v' => array('regular','italic'), 'c' => array('handwriting')),'Playwrite HR' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite HR Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite HR Lijeva' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite HR Lijeva Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite HU' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite HU Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite ID' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite ID Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite IE' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite IE Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite IN' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite IN Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite IS' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite IS Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite IT Moderna' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite IT Moderna Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite IT Trad' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite IT Trad Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite MX' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite MX Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite NG Modern' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite NG Modern Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite NL' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite NL Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite NO' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite NO Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite NZ' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite NZ Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite PE' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite PE Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite PL' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite PL Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite PT' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite PT Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite RO' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite RO Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite SK' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite SK Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite TZ' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite TZ Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite US Modern' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite US Modern Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite US Trad' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite US Trad Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite VN' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite VN Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Playwrite ZA' => array('v' => array('100','200','300','regular'), 'c' => array('handwriting')),'Playwrite ZA Guides' => array('v' => array('regular'), 'c' => array('handwriting')),'Plus Jakarta Sans' => array('v' => array('200','300','regular','500','600','700','800','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Pochaevsk' => array('v' => array('regular'), 'c' => array('display')),'Podkova' => array('v' => array('regular','500','600','700','800'), 'c' => array('serif')),'Poetsen One' => array('v' => array('regular'), 'c' => array('display')),'Poiret One' => array('v' => array('regular'), 'c' => array('display')),'Poller One' => array('v' => array('regular'), 'c' => array('display')),'Poltawski Nowy' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Poly' => array('v' => array('regular','italic'), 'c' => array('serif')),'Pompiere' => array('v' => array('regular'), 'c' => array('display')),'Ponnala' => array('v' => array('regular'), 'c' => array('display')),'Ponomar' => array('v' => array('regular'), 'c' => array('display')),'Pontano Sans' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Poor Story' => array('v' => array('regular'), 'c' => array('display')),'Poppins' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Port Lligat Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Port Lligat Slab' => array('v' => array('regular'), 'c' => array('serif')),'Potta One' => array('v' => array('regular'), 'c' => array('display')),'Pragati Narrow' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Praise' => array('v' => array('regular'), 'c' => array('handwriting')),'Prata' => array('v' => array('regular'), 'c' => array('serif')),'Preahvihear' => array('v' => array('regular'), 'c' => array('sans-serif')),'Press Start 2P' => array('v' => array('regular'), 'c' => array('display')),'Pridi' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('serif')),'Princess Sofia' => array('v' => array('regular'), 'c' => array('handwriting')),'Prociono' => array('v' => array('regular'), 'c' => array('serif')),'Prompt' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Prosto One' => array('v' => array('regular'), 'c' => array('display')),'Protest Guerrilla' => array('v' => array('regular'), 'c' => array('display')),'Protest Revolution' => array('v' => array('regular'), 'c' => array('display')),'Protest Riot' => array('v' => array('regular'), 'c' => array('display')),'Protest Strike' => array('v' => array('regular'), 'c' => array('display')),'Proza Libre' => array('v' => array('regular','italic','500','500italic','600','600italic','700','700italic','800','800italic'), 'c' => array('sans-serif')),'Public Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Puppies Play' => array('v' => array('regular'), 'c' => array('handwriting')),'Puritan' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Purple Purse' => array('v' => array('regular'), 'c' => array('display')),'Qahiri' => array('v' => array('regular'), 'c' => array('sans-serif')),'Quando' => array('v' => array('regular'), 'c' => array('serif')),'Quantico' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Quattrocento' => array('v' => array('regular','700'), 'c' => array('serif')),'Quattrocento Sans' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Questrial' => array('v' => array('regular'), 'c' => array('sans-serif')),'Quicksand' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Quintessential' => array('v' => array('regular'), 'c' => array('handwriting')),'Qwigley' => array('v' => array('regular'), 'c' => array('handwriting')),'Qwitcher Grypen' => array('v' => array('regular','700'), 'c' => array('handwriting')),'REM' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Racing Sans One' => array('v' => array('regular'), 'c' => array('display')),'Radio Canada' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Radio Canada Big' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Radley' => array('v' => array('regular','italic'), 'c' => array('serif')),'Rajdhani' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Rakkas' => array('v' => array('regular'), 'c' => array('display')),'Raleway' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Raleway Dots' => array('v' => array('regular'), 'c' => array('display')),'Ramabhadra' => array('v' => array('regular'), 'c' => array('sans-serif')),'Ramaraja' => array('v' => array('regular'), 'c' => array('serif')),'Rambla' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Rammetto One' => array('v' => array('regular'), 'c' => array('display')),'Rampart One' => array('v' => array('regular'), 'c' => array('display')),'Ranchers' => array('v' => array('regular'), 'c' => array('display')),'Rancho' => array('v' => array('regular'), 'c' => array('handwriting')),'Ranga' => array('v' => array('regular','700'), 'c' => array('display')),'Rasa' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Rationale' => array('v' => array('regular'), 'c' => array('sans-serif')),'Ravi Prakash' => array('v' => array('regular'), 'c' => array('display')),'Readex Pro' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Recursive' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Red Hat Display' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Red Hat Mono' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('monospace')),'Red Hat Text' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Red Rose' => array('v' => array('300','regular','500','600','700'), 'c' => array('display')),'Redacted' => array('v' => array('regular'), 'c' => array('display')),'Redacted Script' => array('v' => array('300','regular','700'), 'c' => array('display')),'Reddit Mono' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('monospace')),'Reddit Sans' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Reddit Sans Condensed' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Redressed' => array('v' => array('regular'), 'c' => array('handwriting')),'Reem Kufi' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Reem Kufi Fun' => array('v' => array('regular','500','600','700'), 'c' => array('sans-serif')),'Reem Kufi Ink' => array('v' => array('regular'), 'c' => array('sans-serif')),'Reenie Beanie' => array('v' => array('regular'), 'c' => array('handwriting')),'Reggae One' => array('v' => array('regular'), 'c' => array('display')),'Rethink Sans' => array('v' => array('regular','500','600','700','800','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Revalia' => array('v' => array('regular'), 'c' => array('display')),'Rhodium Libre' => array('v' => array('regular'), 'c' => array('serif')),'Ribeye' => array('v' => array('regular'), 'c' => array('display')),'Ribeye Marrow' => array('v' => array('regular'), 'c' => array('display')),'Righteous' => array('v' => array('regular'), 'c' => array('display')),'Risque' => array('v' => array('regular'), 'c' => array('display')),'Road Rage' => array('v' => array('regular'), 'c' => array('display')),'Roboto' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Roboto Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Roboto Flex' => array('v' => array('regular'), 'c' => array('sans-serif')),'Roboto Mono' => array('v' => array('100','200','300','regular','500','600','700','100italic','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('monospace')),'Roboto Serif' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Roboto Slab' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('serif')),'Rochester' => array('v' => array('regular'), 'c' => array('handwriting')),'Rock 3D' => array('v' => array('regular'), 'c' => array('display')),'Rock Salt' => array('v' => array('regular'), 'c' => array('handwriting')),'RocknRoll One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Rokkitt' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Romanesco' => array('v' => array('regular'), 'c' => array('handwriting')),'Ropa Sans' => array('v' => array('regular','italic'), 'c' => array('sans-serif')),'Rosario' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('sans-serif')),'Rosarivo' => array('v' => array('regular','italic'), 'c' => array('serif')),'Rouge Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Rowdies' => array('v' => array('300','regular','700'), 'c' => array('display')),'Rozha One' => array('v' => array('regular'), 'c' => array('serif')),'Rubik' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Rubik 80s Fade' => array('v' => array('regular'), 'c' => array('display')),'Rubik Beastly' => array('v' => array('regular'), 'c' => array('display')),'Rubik Broken Fax' => array('v' => array('regular'), 'c' => array('display')),'Rubik Bubbles' => array('v' => array('regular'), 'c' => array('display')),'Rubik Burned' => array('v' => array('regular'), 'c' => array('display')),'Rubik Dirt' => array('v' => array('regular'), 'c' => array('display')),'Rubik Distressed' => array('v' => array('regular'), 'c' => array('display')),'Rubik Doodle Shadow' => array('v' => array('regular'), 'c' => array('display')),'Rubik Doodle Triangles' => array('v' => array('regular'), 'c' => array('display')),'Rubik Gemstones' => array('v' => array('regular'), 'c' => array('display')),'Rubik Glitch' => array('v' => array('regular'), 'c' => array('display')),'Rubik Glitch Pop' => array('v' => array('regular'), 'c' => array('display')),'Rubik Iso' => array('v' => array('regular'), 'c' => array('display')),'Rubik Lines' => array('v' => array('regular'), 'c' => array('display')),'Rubik Maps' => array('v' => array('regular'), 'c' => array('display')),'Rubik Marker Hatch' => array('v' => array('regular'), 'c' => array('display')),'Rubik Maze' => array('v' => array('regular'), 'c' => array('display')),'Rubik Microbe' => array('v' => array('regular'), 'c' => array('display')),'Rubik Mono One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Rubik Moonrocks' => array('v' => array('regular'), 'c' => array('display')),'Rubik Pixels' => array('v' => array('regular'), 'c' => array('display')),'Rubik Puddles' => array('v' => array('regular'), 'c' => array('display')),'Rubik Scribble' => array('v' => array('regular'), 'c' => array('display')),'Rubik Spray Paint' => array('v' => array('regular'), 'c' => array('display')),'Rubik Storm' => array('v' => array('regular'), 'c' => array('display')),'Rubik Vinyl' => array('v' => array('regular'), 'c' => array('display')),'Rubik Wet Paint' => array('v' => array('regular'), 'c' => array('display')),'Ruda' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Rufina' => array('v' => array('regular','700'), 'c' => array('serif')),'Ruge Boogie' => array('v' => array('regular'), 'c' => array('handwriting')),'Ruluko' => array('v' => array('regular'), 'c' => array('sans-serif')),'Rum Raisin' => array('v' => array('regular'), 'c' => array('sans-serif')),'Ruslan Display' => array('v' => array('regular'), 'c' => array('display')),'Russo One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Ruthie' => array('v' => array('regular'), 'c' => array('handwriting')),'Ruwudu' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Rye' => array('v' => array('regular'), 'c' => array('display')),'STIX Two Text' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('serif')),'SUSE' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Sacramento' => array('v' => array('regular'), 'c' => array('handwriting')),'Sahitya' => array('v' => array('regular','700'), 'c' => array('serif')),'Sail' => array('v' => array('regular'), 'c' => array('display')),'Saira' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Saira Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Saira Extra Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Saira Semi Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Saira Stencil One' => array('v' => array('regular'), 'c' => array('display')),'Salsa' => array('v' => array('regular'), 'c' => array('display')),'Sanchez' => array('v' => array('regular','italic'), 'c' => array('serif')),'Sancreek' => array('v' => array('regular'), 'c' => array('display')),'Sankofa Display' => array('v' => array('regular'), 'c' => array('sans-serif')),'Sansation' => array('v' => array('300','300italic','regular','italic','700','700italic'), 'c' => array('sans-serif')),'Sansita' => array('v' => array('regular','italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Sansita Swashed' => array('v' => array('300','regular','500','600','700','800','900'), 'c' => array('display')),'Sarabun' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic'), 'c' => array('sans-serif')),'Sarala' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Sarina' => array('v' => array('regular'), 'c' => array('display')),'Sarpanch' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Sassy Frass' => array('v' => array('regular'), 'c' => array('handwriting')),'Satisfy' => array('v' => array('regular'), 'c' => array('handwriting')),'Sawarabi Gothic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Sawarabi Mincho' => array('v' => array('regular'), 'c' => array('serif')),'Scada' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Scheherazade New' => array('v' => array('regular','500','600','700'), 'c' => array('serif')),'Schibsted Grotesk' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Schoolbell' => array('v' => array('regular'), 'c' => array('handwriting')),'Scope One' => array('v' => array('regular'), 'c' => array('serif')),'Seaweed Script' => array('v' => array('regular'), 'c' => array('display')),'Secular One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Sedan' => array('v' => array('regular','italic'), 'c' => array('serif')),'Sedan SC' => array('v' => array('regular'), 'c' => array('serif')),'Sedgwick Ave' => array('v' => array('regular'), 'c' => array('handwriting')),'Sedgwick Ave Display' => array('v' => array('regular'), 'c' => array('handwriting')),'Sen' => array('v' => array('regular','500','600','700','800'), 'c' => array('sans-serif')),'Send Flowers' => array('v' => array('regular'), 'c' => array('handwriting')),'Sevillana' => array('v' => array('regular'), 'c' => array('display')),'Seymour One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Shadows Into Light' => array('v' => array('regular'), 'c' => array('handwriting')),'Shadows Into Light Two' => array('v' => array('regular'), 'c' => array('handwriting')),'Shafarik' => array('v' => array('regular'), 'c' => array('display')),'Shalimar' => array('v' => array('regular'), 'c' => array('handwriting')),'Shantell Sans' => array('v' => array('300','regular','500','600','700','800','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('display')),'Shanti' => array('v' => array('regular'), 'c' => array('sans-serif')),'Share' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Share Tech' => array('v' => array('regular'), 'c' => array('sans-serif')),'Share Tech Mono' => array('v' => array('regular'), 'c' => array('monospace')),'Shippori Antique' => array('v' => array('regular'), 'c' => array('sans-serif')),'Shippori Antique B1' => array('v' => array('regular'), 'c' => array('sans-serif')),'Shippori Mincho' => array('v' => array('regular','500','600','700','800'), 'c' => array('serif')),'Shippori Mincho B1' => array('v' => array('regular','500','600','700','800'), 'c' => array('serif')),'Shizuru' => array('v' => array('regular'), 'c' => array('display')),'Shojumaru' => array('v' => array('regular'), 'c' => array('display')),'Short Stack' => array('v' => array('regular'), 'c' => array('handwriting')),'Shrikhand' => array('v' => array('regular'), 'c' => array('display')),'Siemreap' => array('v' => array('regular'), 'c' => array('sans-serif')),'Sigmar' => array('v' => array('regular'), 'c' => array('display')),'Sigmar One' => array('v' => array('regular'), 'c' => array('display')),'Signika' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Signika Negative' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Silkscreen' => array('v' => array('regular','700'), 'c' => array('display')),'Simonetta' => array('v' => array('regular','italic','900','900italic'), 'c' => array('display')),'Single Day' => array('v' => array('regular'), 'c' => array('display')),'Sintony' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Sirin Stencil' => array('v' => array('regular'), 'c' => array('display')),'Six Caps' => array('v' => array('regular'), 'c' => array('sans-serif')),'Sixtyfour' => array('v' => array('regular'), 'c' => array('monospace')),'Sixtyfour Convergence' => array('v' => array('regular'), 'c' => array('monospace')),'Skranji' => array('v' => array('regular','700'), 'c' => array('display')),'Slabo 13px' => array('v' => array('regular'), 'c' => array('serif')),'Slabo 27px' => array('v' => array('regular'), 'c' => array('serif')),'Slackey' => array('v' => array('regular'), 'c' => array('display')),'Slackside One' => array('v' => array('regular'), 'c' => array('handwriting')),'Smokum' => array('v' => array('regular'), 'c' => array('display')),'Smooch' => array('v' => array('regular'), 'c' => array('handwriting')),'Smooch Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Smythe' => array('v' => array('regular'), 'c' => array('display')),'Sniglet' => array('v' => array('regular','800'), 'c' => array('display')),'Snippet' => array('v' => array('regular'), 'c' => array('sans-serif')),'Snowburst One' => array('v' => array('regular'), 'c' => array('display')),'Sofadi One' => array('v' => array('regular'), 'c' => array('display')),'Sofia' => array('v' => array('regular'), 'c' => array('handwriting')),'Sofia Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Sofia Sans Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Sofia Sans Extra Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Sofia Sans Semi Condensed' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Solitreo' => array('v' => array('regular'), 'c' => array('handwriting')),'Solway' => array('v' => array('300','regular','500','700','800'), 'c' => array('serif')),'Sometype Mono' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('monospace')),'Song Myung' => array('v' => array('regular'), 'c' => array('serif')),'Sono' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Sonsie One' => array('v' => array('regular'), 'c' => array('display')),'Sora' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Sorts Mill Goudy' => array('v' => array('regular','italic'), 'c' => array('serif')),'Sour Gummy' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Source Code Pro' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('monospace')),'Source Sans 3' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Source Serif 4' => array('v' => array('200','300','regular','500','600','700','800','900','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Space Grotesk' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Space Mono' => array('v' => array('regular','italic','700','700italic'), 'c' => array('monospace')),'Special Elite' => array('v' => array('regular'), 'c' => array('display')),'Special Gothic' => array('v' => array('regular'), 'c' => array('sans-serif')),'Special Gothic Condensed One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Special Gothic Expanded One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Spectral' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic'), 'c' => array('serif')),'Spectral SC' => array('v' => array('200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic'), 'c' => array('serif')),'Spicy Rice' => array('v' => array('regular'), 'c' => array('display')),'Spinnaker' => array('v' => array('regular'), 'c' => array('sans-serif')),'Spirax' => array('v' => array('regular'), 'c' => array('display')),'Splash' => array('v' => array('regular'), 'c' => array('handwriting')),'Spline Sans' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Spline Sans Mono' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('monospace')),'Squada One' => array('v' => array('regular'), 'c' => array('display')),'Square Peg' => array('v' => array('regular'), 'c' => array('handwriting')),'Sree Krushnadevaraya' => array('v' => array('regular'), 'c' => array('serif')),'Sriracha' => array('v' => array('regular'), 'c' => array('handwriting')),'Srisakdi' => array('v' => array('regular','700'), 'c' => array('display')),'Staatliches' => array('v' => array('regular'), 'c' => array('display')),'Stalemate' => array('v' => array('regular'), 'c' => array('handwriting')),'Stalinist One' => array('v' => array('regular'), 'c' => array('display')),'Stardos Stencil' => array('v' => array('regular','700'), 'c' => array('display')),'Stick' => array('v' => array('regular'), 'c' => array('sans-serif')),'Stick No Bills' => array('v' => array('200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Stint Ultra Condensed' => array('v' => array('regular'), 'c' => array('serif')),'Stint Ultra Expanded' => array('v' => array('regular'), 'c' => array('serif')),'Stoke' => array('v' => array('300','regular'), 'c' => array('serif')),'Strait' => array('v' => array('regular'), 'c' => array('sans-serif')),'Style Script' => array('v' => array('regular'), 'c' => array('handwriting')),'Stylish' => array('v' => array('regular'), 'c' => array('sans-serif')),'Sue Ellen Francisco' => array('v' => array('regular'), 'c' => array('handwriting')),'Suez One' => array('v' => array('regular'), 'c' => array('serif')),'Sulphur Point' => array('v' => array('300','regular','700'), 'c' => array('sans-serif')),'Sumana' => array('v' => array('regular','700'), 'c' => array('serif')),'Sunflower' => array('v' => array('300','500','700'), 'c' => array('sans-serif')),'Sunshiney' => array('v' => array('regular'), 'c' => array('handwriting')),'Supermercado One' => array('v' => array('regular'), 'c' => array('display')),'Sura' => array('v' => array('regular','700'), 'c' => array('serif')),'Suranna' => array('v' => array('regular'), 'c' => array('serif')),'Suravaram' => array('v' => array('regular'), 'c' => array('serif')),'Suwannaphum' => array('v' => array('100','300','regular','700','900'), 'c' => array('serif')),'Swanky and Moo Moo' => array('v' => array('regular'), 'c' => array('handwriting')),'Syncopate' => array('v' => array('regular','700'), 'c' => array('sans-serif')),'Syne' => array('v' => array('regular','500','600','700','800'), 'c' => array('sans-serif')),'Syne Mono' => array('v' => array('regular'), 'c' => array('monospace')),'Syne Tactile' => array('v' => array('regular'), 'c' => array('display')),'Tac One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Tagesschrift' => array('v' => array('regular'), 'c' => array('display')),'Tai Heritage Pro' => array('v' => array('regular','700'), 'c' => array('serif')),'Tajawal' => array('v' => array('200','300','regular','500','700','800','900'), 'c' => array('sans-serif')),'Tangerine' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Tapestry' => array('v' => array('regular'), 'c' => array('handwriting')),'Taprom' => array('v' => array('regular'), 'c' => array('display')),'Tauri' => array('v' => array('regular'), 'c' => array('sans-serif')),'Taviraj' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('serif')),'Teachers' => array('v' => array('regular','500','600','700','800','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Teko' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Tektur' => array('v' => array('regular','500','600','700','800','900'), 'c' => array('display')),'Telex' => array('v' => array('regular'), 'c' => array('sans-serif')),'Tenali Ramakrishna' => array('v' => array('regular'), 'c' => array('sans-serif')),'Tenor Sans' => array('v' => array('regular'), 'c' => array('sans-serif')),'Text Me One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Texturina' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Thasadith' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'The Girl Next Door' => array('v' => array('regular'), 'c' => array('handwriting')),'The Nautigal' => array('v' => array('regular','700'), 'c' => array('handwriting')),'Tienne' => array('v' => array('regular','700','900'), 'c' => array('serif')),'Tillana' => array('v' => array('regular','500','600','700','800'), 'c' => array('display')),'Tilt Neon' => array('v' => array('regular'), 'c' => array('display')),'Tilt Prism' => array('v' => array('regular'), 'c' => array('display')),'Tilt Warp' => array('v' => array('regular'), 'c' => array('display')),'Timmana' => array('v' => array('regular'), 'c' => array('sans-serif')),'Tinos' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Tiny5' => array('v' => array('regular'), 'c' => array('sans-serif')),'Tiro Bangla' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Devanagari Hindi' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Devanagari Marathi' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Devanagari Sanskrit' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Gurmukhi' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Kannada' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Tamil' => array('v' => array('regular','italic'), 'c' => array('serif')),'Tiro Telugu' => array('v' => array('regular','italic'), 'c' => array('serif')),'Titan One' => array('v' => array('regular'), 'c' => array('display')),'Titillium Web' => array('v' => array('200','200italic','300','300italic','regular','italic','600','600italic','700','700italic','900'), 'c' => array('sans-serif')),'Tomorrow' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('sans-serif')),'Tourney' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('display')),'Trade Winds' => array('v' => array('regular'), 'c' => array('display')),'Train One' => array('v' => array('regular'), 'c' => array('display')),'Triodion' => array('v' => array('regular'), 'c' => array('display')),'Trirong' => array('v' => array('100','100italic','200','200italic','300','300italic','regular','italic','500','500italic','600','600italic','700','700italic','800','800italic','900','900italic'), 'c' => array('serif')),'Trispace' => array('v' => array('100','200','300','regular','500','600','700','800'), 'c' => array('sans-serif')),'Trocchi' => array('v' => array('regular'), 'c' => array('serif')),'Trochut' => array('v' => array('regular','italic','700'), 'c' => array('display')),'Truculenta' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Trykker' => array('v' => array('regular'), 'c' => array('serif')),'Tsukimi Rounded' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Tuffy' => array('v' => array('regular','italic','700','700italic'), 'c' => array('sans-serif')),'Tulpen One' => array('v' => array('regular'), 'c' => array('display')),'Turret Road' => array('v' => array('200','300','regular','500','700','800'), 'c' => array('display')),'Twinkle Star' => array('v' => array('regular'), 'c' => array('handwriting')),'Ubuntu' => array('v' => array('300','300italic','regular','italic','500','500italic','700','700italic'), 'c' => array('sans-serif')),'Ubuntu Condensed' => array('v' => array('regular'), 'c' => array('sans-serif')),'Ubuntu Mono' => array('v' => array('regular','italic','700','700italic'), 'c' => array('monospace')),'Ubuntu Sans' => array('v' => array('100','200','300','regular','500','600','700','800','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic'), 'c' => array('sans-serif')),'Ubuntu Sans Mono' => array('v' => array('regular','500','600','700','italic','500italic','600italic','700italic'), 'c' => array('monospace')),'Uchen' => array('v' => array('regular'), 'c' => array('serif')),'Ultra' => array('v' => array('regular'), 'c' => array('serif')),'Unbounded' => array('v' => array('200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Uncial Antiqua' => array('v' => array('regular'), 'c' => array('display')),'Underdog' => array('v' => array('regular'), 'c' => array('display')),'Unica One' => array('v' => array('regular'), 'c' => array('display')),'UnifrakturCook' => array('v' => array('700'), 'c' => array('display')),'UnifrakturMaguntia' => array('v' => array('regular'), 'c' => array('display')),'Unkempt' => array('v' => array('regular','700'), 'c' => array('display')),'Unlock' => array('v' => array('regular'), 'c' => array('display')),'Unna' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Updock' => array('v' => array('regular'), 'c' => array('handwriting')),'Urbanist' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'VT323' => array('v' => array('regular'), 'c' => array('monospace')),'Vampiro One' => array('v' => array('regular'), 'c' => array('display')),'Varela' => array('v' => array('regular'), 'c' => array('sans-serif')),'Varela Round' => array('v' => array('regular'), 'c' => array('sans-serif')),'Varta' => array('v' => array('300','regular','500','600','700'), 'c' => array('sans-serif')),'Vast Shadow' => array('v' => array('regular'), 'c' => array('serif')),'Vazirmatn' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Vesper Libre' => array('v' => array('regular','500','700','900'), 'c' => array('serif')),'Viaoda Libre' => array('v' => array('regular'), 'c' => array('display')),'Vibes' => array('v' => array('regular'), 'c' => array('display')),'Vibur' => array('v' => array('regular'), 'c' => array('handwriting')),'Victor Mono' => array('v' => array('100','200','300','regular','500','600','700','100italic','200italic','300italic','italic','500italic','600italic','700italic'), 'c' => array('monospace')),'Vidaloka' => array('v' => array('regular'), 'c' => array('serif')),'Viga' => array('v' => array('regular'), 'c' => array('sans-serif')),'Vina Sans' => array('v' => array('regular'), 'c' => array('display')),'Voces' => array('v' => array('regular'), 'c' => array('sans-serif')),'Volkhov' => array('v' => array('regular','italic','700','700italic'), 'c' => array('serif')),'Vollkorn' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Vollkorn SC' => array('v' => array('regular','600','700','900'), 'c' => array('serif')),'Voltaire' => array('v' => array('regular'), 'c' => array('sans-serif')),'Vujahday Script' => array('v' => array('regular'), 'c' => array('handwriting')),'WDXL Lubrifont TC' => array('v' => array('regular'), 'c' => array('sans-serif')),'Waiting for the Sunrise' => array('v' => array('regular'), 'c' => array('handwriting')),'Wallpoet' => array('v' => array('regular'), 'c' => array('display')),'Walter Turncoat' => array('v' => array('regular'), 'c' => array('handwriting')),'Warnes' => array('v' => array('regular'), 'c' => array('display')),'Water Brush' => array('v' => array('regular'), 'c' => array('handwriting')),'Waterfall' => array('v' => array('regular'), 'c' => array('handwriting')),'Wavefont' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('display')),'Wellfleet' => array('v' => array('regular'), 'c' => array('serif')),'Wendy One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Whisper' => array('v' => array('regular'), 'c' => array('handwriting')),'WindSong' => array('v' => array('regular','500'), 'c' => array('handwriting')),'Winky Rough' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Winky Sans' => array('v' => array('300','regular','500','600','700','800','900','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Wire One' => array('v' => array('regular'), 'c' => array('sans-serif')),'Wittgenstein' => array('v' => array('regular','500','600','700','800','900','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('serif')),'Wix Madefor Display' => array('v' => array('regular','500','600','700','800'), 'c' => array('sans-serif')),'Wix Madefor Text' => array('v' => array('regular','italic','500','500italic','600','600italic','700','700italic','800','800italic'), 'c' => array('sans-serif')),'Work Sans' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Workbench' => array('v' => array('regular'), 'c' => array('monospace')),'Xanh Mono' => array('v' => array('regular','italic'), 'c' => array('monospace')),'Yaldevi' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Yanone Kaffeesatz' => array('v' => array('200','300','regular','500','600','700'), 'c' => array('sans-serif')),'Yantramanav' => array('v' => array('100','300','regular','500','700','900'), 'c' => array('sans-serif')),'Yarndings 12' => array('v' => array('regular'), 'c' => array('display')),'Yarndings 12 Charted' => array('v' => array('regular'), 'c' => array('display')),'Yarndings 20' => array('v' => array('regular'), 'c' => array('display')),'Yarndings 20 Charted' => array('v' => array('regular'), 'c' => array('display')),'Yatra One' => array('v' => array('regular'), 'c' => array('display')),'Yellowtail' => array('v' => array('regular'), 'c' => array('handwriting')),'Yeon Sung' => array('v' => array('regular'), 'c' => array('display')),'Yeseva One' => array('v' => array('regular'), 'c' => array('display')),'Yesteryear' => array('v' => array('regular'), 'c' => array('handwriting')),'Yomogi' => array('v' => array('regular'), 'c' => array('handwriting')),'Young Serif' => array('v' => array('regular'), 'c' => array('serif')),'Yrsa' => array('v' => array('300','regular','500','600','700','300italic','italic','500italic','600italic','700italic'), 'c' => array('serif')),'Ysabeau' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Ysabeau Infant' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Ysabeau Office' => array('v' => array('100','200','300','regular','500','600','700','800','900','100italic','200italic','300italic','italic','500italic','600italic','700italic','800italic','900italic'), 'c' => array('sans-serif')),'Ysabeau SC' => array('v' => array('100','200','300','regular','500','600','700','800','900'), 'c' => array('sans-serif')),'Yuji Boku' => array('v' => array('regular'), 'c' => array('serif')),'Yuji Hentaigana Akari' => array('v' => array('regular'), 'c' => array('handwriting')),'Yuji Hentaigana Akebono' => array('v' => array('regular'), 'c' => array('handwriting')),'Yuji Mai' => array('v' => array('regular'), 'c' => array('serif')),'Yuji Syuku' => array('v' => array('regular'), 'c' => array('serif')),'Yusei Magic' => array('v' => array('regular'), 'c' => array('sans-serif')),'ZCOOL KuaiLe' => array('v' => array('regular'), 'c' => array('sans-serif')),'ZCOOL QingKe HuangYou' => array('v' => array('regular'), 'c' => array('sans-serif')),'ZCOOL XiaoWei' => array('v' => array('regular'), 'c' => array('sans-serif')),'Zain' => array('v' => array('200','300','300italic','regular','italic','700','800','900'), 'c' => array('sans-serif')),'Zen Antique' => array('v' => array('regular'), 'c' => array('serif')),'Zen Antique Soft' => array('v' => array('regular'), 'c' => array('serif')),'Zen Dots' => array('v' => array('regular'), 'c' => array('display')),'Zen Kaku Gothic Antique' => array('v' => array('300','regular','500','700','900'), 'c' => array('sans-serif')),'Zen Kaku Gothic New' => array('v' => array('300','regular','500','700','900'), 'c' => array('sans-serif')),'Zen Kurenaido' => array('v' => array('regular'), 'c' => array('sans-serif')),'Zen Loop' => array('v' => array('regular','italic'), 'c' => array('display')),'Zen Maru Gothic' => array('v' => array('300','regular','500','700','900'), 'c' => array('sans-serif')),'Zen Old Mincho' => array('v' => array('regular','500','600','700','900'), 'c' => array('serif')),'Zen Tokyo Zoo' => array('v' => array('regular'), 'c' => array('display')),'Zeyada' => array('v' => array('regular'), 'c' => array('handwriting')),'Zhi Mang Xing' => array('v' => array('regular'), 'c' => array('handwriting')),'Zilla Slab' => array('v' => array('300','300italic','regular','italic','500','500italic','600','600italic','700','700italic'), 'c' => array('serif')),'Zilla Slab Highlight' => array('v' => array('regular','700'), 'c' => array('serif')));