<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'AmericaneagleWpMUPlugin\\Admin\\Dashboard' => $baseDir . '/includes/classes/Admin/Dashboard.php',
    'AmericaneagleWpMUPlugin\\Admin\\Login' => $baseDir . '/includes/classes/Admin/Login.php',
    'AmericaneagleWpMUPlugin\\Admin\\Plugins' => $baseDir . '/includes/classes/Admin/Plugins.php',
    'AmericaneagleWpMUPlugin\\Admin\\SamlSso' => $baseDir . '/includes/classes/Admin/SamlSso.php',
    'AmericaneagleWpMUPlugin\\BaseAbstract' => $baseDir . '/includes/classes/BaseAbstract.php',
    'AmericaneagleWpMUPlugin\\Core\\Core' => $baseDir . '/includes/classes/Core/Core.php',
    'AmericaneagleWpMUPlugin\\Cron\\PluginChecker' => $baseDir . '/includes/classes/Cron/PluginChecker.php',
    'AmericaneagleWpMUPlugin\\Events\\PluginRemoval' => $baseDir . '/includes/classes/Events/PluginRemoval.php',
    'AmericaneagleWpMUPlugin\\Helper\\ContainerTrait' => $baseDir . '/includes/classes/Helper/ContainerTrait.php',
    'AmericaneagleWpMUPlugin\\Notifications\\Email' => $baseDir . '/includes/classes/Notifications/Email.php',
    'AmericaneagleWpMUPlugin\\Plugin' => $baseDir . '/includes/classes/Plugin.php',
    'AmericaneagleWpMUPlugin\\Updater\\AeWpMuUpdater' => $baseDir . '/includes/classes/Updater/AeWpMuUpdater.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'League\\Container\\Argument\\ArgumentInterface' => $vendorDir . '/league/container/src/Argument/ArgumentInterface.php',
    'League\\Container\\Argument\\ArgumentResolverInterface' => $vendorDir . '/league/container/src/Argument/ArgumentResolverInterface.php',
    'League\\Container\\Argument\\ArgumentResolverTrait' => $vendorDir . '/league/container/src/Argument/ArgumentResolverTrait.php',
    'League\\Container\\Argument\\DefaultValueArgument' => $vendorDir . '/league/container/src/Argument/DefaultValueArgument.php',
    'League\\Container\\Argument\\DefaultValueInterface' => $vendorDir . '/league/container/src/Argument/DefaultValueInterface.php',
    'League\\Container\\Argument\\LiteralArgument' => $vendorDir . '/league/container/src/Argument/LiteralArgument.php',
    'League\\Container\\Argument\\LiteralArgumentInterface' => $vendorDir . '/league/container/src/Argument/LiteralArgumentInterface.php',
    'League\\Container\\Argument\\Literal\\ArrayArgument' => $vendorDir . '/league/container/src/Argument/Literal/ArrayArgument.php',
    'League\\Container\\Argument\\Literal\\BooleanArgument' => $vendorDir . '/league/container/src/Argument/Literal/BooleanArgument.php',
    'League\\Container\\Argument\\Literal\\CallableArgument' => $vendorDir . '/league/container/src/Argument/Literal/CallableArgument.php',
    'League\\Container\\Argument\\Literal\\FloatArgument' => $vendorDir . '/league/container/src/Argument/Literal/FloatArgument.php',
    'League\\Container\\Argument\\Literal\\IntegerArgument' => $vendorDir . '/league/container/src/Argument/Literal/IntegerArgument.php',
    'League\\Container\\Argument\\Literal\\ObjectArgument' => $vendorDir . '/league/container/src/Argument/Literal/ObjectArgument.php',
    'League\\Container\\Argument\\Literal\\StringArgument' => $vendorDir . '/league/container/src/Argument/Literal/StringArgument.php',
    'League\\Container\\Argument\\ResolvableArgument' => $vendorDir . '/league/container/src/Argument/ResolvableArgument.php',
    'League\\Container\\Argument\\ResolvableArgumentInterface' => $vendorDir . '/league/container/src/Argument/ResolvableArgumentInterface.php',
    'League\\Container\\Container' => $vendorDir . '/league/container/src/Container.php',
    'League\\Container\\ContainerAwareInterface' => $vendorDir . '/league/container/src/ContainerAwareInterface.php',
    'League\\Container\\ContainerAwareTrait' => $vendorDir . '/league/container/src/ContainerAwareTrait.php',
    'League\\Container\\DefinitionContainerInterface' => $vendorDir . '/league/container/src/DefinitionContainerInterface.php',
    'League\\Container\\Definition\\Definition' => $vendorDir . '/league/container/src/Definition/Definition.php',
    'League\\Container\\Definition\\DefinitionAggregate' => $vendorDir . '/league/container/src/Definition/DefinitionAggregate.php',
    'League\\Container\\Definition\\DefinitionAggregateInterface' => $vendorDir . '/league/container/src/Definition/DefinitionAggregateInterface.php',
    'League\\Container\\Definition\\DefinitionInterface' => $vendorDir . '/league/container/src/Definition/DefinitionInterface.php',
    'League\\Container\\Exception\\ContainerException' => $vendorDir . '/league/container/src/Exception/ContainerException.php',
    'League\\Container\\Exception\\NotFoundException' => $vendorDir . '/league/container/src/Exception/NotFoundException.php',
    'League\\Container\\Inflector\\Inflector' => $vendorDir . '/league/container/src/Inflector/Inflector.php',
    'League\\Container\\Inflector\\InflectorAggregate' => $vendorDir . '/league/container/src/Inflector/InflectorAggregate.php',
    'League\\Container\\Inflector\\InflectorAggregateInterface' => $vendorDir . '/league/container/src/Inflector/InflectorAggregateInterface.php',
    'League\\Container\\Inflector\\InflectorInterface' => $vendorDir . '/league/container/src/Inflector/InflectorInterface.php',
    'League\\Container\\ReflectionContainer' => $vendorDir . '/league/container/src/ReflectionContainer.php',
    'League\\Container\\ServiceProvider\\AbstractServiceProvider' => $vendorDir . '/league/container/src/ServiceProvider/AbstractServiceProvider.php',
    'League\\Container\\ServiceProvider\\BootableServiceProviderInterface' => $vendorDir . '/league/container/src/ServiceProvider/BootableServiceProviderInterface.php',
    'League\\Container\\ServiceProvider\\ServiceProviderAggregate' => $vendorDir . '/league/container/src/ServiceProvider/ServiceProviderAggregate.php',
    'League\\Container\\ServiceProvider\\ServiceProviderAggregateInterface' => $vendorDir . '/league/container/src/ServiceProvider/ServiceProviderAggregateInterface.php',
    'League\\Container\\ServiceProvider\\ServiceProviderInterface' => $vendorDir . '/league/container/src/ServiceProvider/ServiceProviderInterface.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
);
