<?php
# Database Configuration
define( 'DB_NAME', 'site-nscc' );
define( 'DB_USER', 'root' );
define( 'DB_PASSWORD', 'root' );
define( 'DB_HOST', 'localhost' );
define( 'DB_HOST_SLAVE', 'localhost' );
define('DB_CHARSET', 'utf8');
define('DB_COLLATE', 'utf8_unicode_ci');
$table_prefix = 'wp_';

# Security Salts, Keys, Etc
define('AUTH_KEY',         'b9iH&x:5)0+p</+b(1|A+,gyXMow3ZW%3ML`-A}F&>0YkT 0/4hP#UG8aFpGYnu.');
define('SECURE_AUTH_KEY',  'd-Hr 63ce98!(!UY.L7*uQV#A-hW@ZH-T&s*&aJHh%XkxIm9;gC5<eR%%z|2IY>.');
define('LOGGED_IN_KEY',    'e@f+*rAEY9c%]-QIi|;tqeG&RgVpxm7TH$LI^1FwD)Fi@3YMxMa$t7Jo,z7.A!5>');
define('NONCE_KEY',        '!zdnoe}uL`w|{m&%@rFsa23fT=5jw|k1`|v:H`ooG<mEoTp([VT1bgqftEh.GMnQ');
define('AUTH_SALT',        'qF:#[Yl5njPysAcepsJG|&Dy|%Z1oNKB+8${t/[j_+*E-d%Yc8Q@d,;:FtnJx^:j');
define('SECURE_AUTH_SALT', '|gD1afn=Ka<8^k2oBd>Wnl|Xn9t/s`)9WJ|Cw d%W+|GGjG.<<YN|8C4SQ*gU;>A');
define('LOGGED_IN_SALT',   '_K9tdf3`UqlQJ2AOwb9pP<=&&ldYr/-ePZoQl&/qfZu=?E$f~d/oURr4OHrwIL;+');
define('NONCE_SALT',       'v-qXo4uH10S;ZW&`F%T,|AdDMnG9O[ M{e.8FHy!Q{Vqgw0Ft:Y.DY{{ |I-YPe/');


# Localized Language Stuff

define( 'WP_CACHE', TRUE );

define( 'WP_AUTO_UPDATE_CORE', false );

define( 'PWP_NAME', 'nshorecaredev' );

define( 'FS_METHOD', 'direct' );

define( 'FS_CHMOD_DIR', 0775 );

define( 'FS_CHMOD_FILE', 0664 );

define( 'WPE_APIKEY', '4805abef505e49eae9e341d2ae608e055b1f3c5c' );

define( 'WPE_CLUSTER_ID', '219888' );

define( 'WPE_CLUSTER_TYPE', 'pod' );

define( 'WPE_ISP', true );

define( 'WPE_BPOD', false );

define( 'WPE_RO_FILESYSTEM', false );

define( 'WPE_LARGEFS_BUCKET', 'largefs.wpengine' );

define( 'WPE_SFTP_PORT', 2222 );

define( 'WPE_SFTP_ENDPOINT', '**************' );

define( 'WPE_LBMASTER_IP', '' );

define( 'WPE_CDN_DISABLE_ALLOWED', true );

define( 'DISALLOW_FILE_MODS', FALSE );

define( 'DISALLOW_FILE_EDIT', FALSE );

define( 'DISABLE_WP_CRON', false );

define( 'WPE_FORCE_SSL_LOGIN', false );

define( 'FORCE_SSL_LOGIN', false );

/*SSLSTART*/ if ( isset($_SERVER['HTTP_X_WPE_SSL']) && $_SERVER['HTTP_X_WPE_SSL'] ) $_SERVER['HTTPS'] = 'on'; /*SSLEND*/

define( 'WPE_EXTERNAL_URL', false );

define( 'WP_POST_REVISIONS', FALSE );

define( 'WPE_WHITELABEL', 'wpengine' );

define( 'WP_TURN_OFF_ADMIN_BAR', false );

define( 'WPE_BETA_TESTER', false );

umask(0002);

$wpe_cdn_uris=array ( );

$wpe_no_cdn_uris=array ( );

$wpe_content_regexs=array ( );

$wpe_all_domains=array ( 0 => 'nshorecaredev.wpengine.com', 1 => 'nshorecaredev.wpenginepowered.com', );

$wpe_varnish_servers=array ( 0 => '127.0.0.1', );

$wpe_special_ips=array ( 0 => '*************', 1 => 'pod-219888-utility.pod-219888.svc.cluster.local', );

$wpe_netdna_domains=array ( );

$wpe_netdna_domains_secure=array ( );

$wpe_netdna_push_domains=array ( );

$wpe_domain_mappings=array ( );

$memcached_servers=array ( 'default' =>  array ( 0 => 'unix:///tmp/memcached.sock', ), );
define('WPLANG','');

# WP Engine ID


# WP Engine Settings






# That's It. Pencils down
if ( !defined('ABSPATH') )
	define('ABSPATH', __DIR__ . '/');
require_once(ABSPATH . 'wp-settings.php');
