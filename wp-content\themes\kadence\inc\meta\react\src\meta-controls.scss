.kadence-title-control-bar {
    display: block;
    position: relative;
    margin-bottom: 10px;
}
.kadence-control-field {
	.customize-control-title {
		font-weight: 600;
		margin-bottom: 0;
		display: -webkit-box;
		display: flex;
		-webkit-box-align: center;
		align-items: center;
		letter-spacing: 0.1px;
		line-height: 1.4;
		margin-top: 10px;
		padding-top: 8px;
		color: #555d66;
	}
	button.components-button.kadence-reset {
		height: 18px;
		padding: 0 5px;
		margin-right: 0;
		margin-left: -20px;
		opacity: 0.5;
		svg {
			width: 12px;
			height: 12px;
		}
	}
	.components-button-group.kadence-radio-container-control {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		column-gap: 10px;
		row-gap: 10px;
		padding: 10px 0;
		border: 0;
		button.components-button.is-tertiary {
			flex: 1 1 0;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 11px;
			font-weight: 600;
			font-style: normal;
			text-transform: uppercase;
			height: 60px;
			margin: 0;
			padding: 4px;
			border: 1px solid #CBD5E0;
			border-radius: 2px;
			background: transparent;
			color: #4A5568;
			box-shadow: none;
			&.active-radio {
				border-color: #007cba;
				background: #007cba;
				color: #fff;
				box-shadow: none !important;
			}
			.kadence-radio-icon {
				width: 100%;
				height: 100%;
				display:flex;
				svg {
					width: 100%;
					height: auto;
					max-height: 100%;
				}
			}
		}
	}
}
.kadence-control-field.three-col-short .components-button-group.kadence-radio-container-control button.components-button.is-tertiary {
    height:auto;
    min-height:30px;
}

.kadence-control-field.two-col-square .components-button-group.kadence-radio-container-control {
    grid-template-columns: 1fr 1fr;
}
.kadence-control-field.two-col-short .components-button-group.kadence-radio-container-control button.components-button.is-tertiary {
    height: auto;
    min-height: 30px;
}
.kadence-control-field.two-col-short .components-button-group.kadence-radio-container-control {
    grid-template-columns: 1fr 1fr;
}
.kadence-control-field.two-col-square .components-button-group.kadence-radio-container-control button.components-button.is-tertiary {
    height:50px;
}
.kadence-sidebar-container.components-panel__body.is-opened {
    padding-top: 2px;
}
.kadence-control-field.three-col-square .components-button-group.kadence-radio-container-control button.components-button.is-tertiary {
	height:90px;
	.kadence-radio-icon svg {
		height: 60px;
	}
}
.kadence-control-field.three-col-square .components-button-group.kadence-radio-container-control button.components-button.is-tertiary.btn-flex-col {
    flex-direction: column;
	font-size: 8px;
}