<?php
/**
 * Creates a cron job to check for blacklisted plugins.
 *
 * @package AmericaneagleWpMUPlugin\Cron
 */

namespace AmericaneagleWpMUPlugin\Cron;

use AmericaneagleWpMUPlugin\BaseAbstract;

require_once( ABSPATH . 'wp-admin/includes/plugin.php' );

/**
 * Class PluginChecker
 */
class PluginChecker extends BaseAbstract {

	/**
	 * Registers actions & filters.
	 *
	 * @return void
	 */
	public function register(): void {
		// Create schedule for email notification.
		add_filter(
			'cron_schedules',
			function ( $schedules ) {
				$schedules['every_six_hours'] = [
					'interval' => 6 * HOUR_IN_SECONDS,
					'display'  => __( 'Every 6 hrs' ),
				];

				return $schedules;
			}
		);

		// Notifiy admin about the upcoming deletion of blacklisted plugins.
		if ( ! \wp_next_scheduled( 'ae_plugin_checker' ) ) {
			\wp_schedule_event( time(), 'every_six_hours', 'ae_plugin_checker' );
		}

		add_action( 'ae_plugin_checker', [ $this, 'blacklisted_plugins' ] );
	}

	/**
	 * Identifies blacklisted plugins and notifies admin of upcoming deletion.
	 *
	 * @return void
	 */
	public function blacklisted_plugins(): void {
		$plugins_to_delete = false;
		$current_plugins   = get_plugins();

		// Check if any plugins are blacklisted.
		foreach( self::BLACKLISTED_PLUGINS as $blocked_plugin ) {
			if ( isset( $current_plugins[ $blocked_plugin ] ) ) {
				$plugins_to_delete = true;
				break;
			}
		}

		$wp_cron_tasks = get_option('cron');

		// Exit function if the delete plugins scheduled task is already set.
		if ( ! empty( $wp_cron_tasks ) ) {
			foreach ( $wp_cron_tasks as $task ) {
				if ( isset( $task['delete_blacklisted_plugins'] ) ) {
					return;
				}
			}
		}

		if ( $plugins_to_delete ) {
			$notification_days = [
				0,
				5,
				6,
			];

			// Schedule sending email notifications.
			foreach ( $notification_days as $email_day ) {
				$email_seconds = $email_day * DAY_IN_SECONDS;

				wp_schedule_single_event( time() + $email_seconds, 'email_blacklisted_plugins_' . $email_day, [ $email_seconds ] );
			}
				
			// Schedule plugins for deletion.
			$delete_time    = 7;
			$delete_seconds = $delete_time * DAY_IN_SECONDS;
			wp_schedule_single_event( time() + $delete_seconds, 'delete_blacklisted_plugins' );
		}
	}
}
