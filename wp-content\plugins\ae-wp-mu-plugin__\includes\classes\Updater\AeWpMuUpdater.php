<?php
/**
 * Handles plugin updates.
 *
 * @package AmericaneagleWpMUPlugin\Updater
 */

namespace AmericaneagleWpMUPlugin\Updater;

use AmericaneagleWpMUPlugin\BaseAbstract;

class AeWpMuUpdater extends BaseAbstract {
    /**
     * Plugin slug.
     *
     * @var string
     */
    public string $plugin_slug;

    /**
     * Version number.
     *
     * @var string
     */
    public string $version;

    /**
     * Cache Key.
     *
     * @var string
     */
    public string $cache_key;

    /**
     * Is cache allowed.
     *
     * @var bool
     */
    public bool $cache_allowed;

    /**
     * Basic auth username.
     *
     * @var string
     */
    public string $username;

    /**
     * Basic auth password.
     *
     * @var string
     */
    public string $password;

    /**
     * Sets .
     */
    public function __construct() {
        parent::__construct();

        $this->plugin_slug   = plugin_basename( AMERICANEAGLE_WP_MU_PLUGIN_PATH );
        $this->version       = AMERICANEAGLE_WP_MU_PLUGIN_VERSION;
        $this->cache_key     = 'ae_wp_mu_plugin_updater';
        $this->cache_allowed = false;
        $this->username      = 'aepackagist';
        $this->password      = 'design';
    }

    /**
     * Makes a remote request to get the updated plugin
     *
     * @return bool|array|object
     */
    public function request(): bool|array|object {
        $remote = get_transient( $this->cache_key );

        if ( false === $remote || ! $this->cache_allowed ) {
            $remote = wp_remote_get( 'https://aepackagist.wpengine.com/releases/ae-wp-mu-plugin.json', [
                    'headers' => [
                        'Authorization' => 'Basic ' . base64_encode( $this->username . ':' . $this->password ), // phpcs:ignore
                        'Accept'        => 'application/json'
                    ]
                ]
            );

            if ( is_wp_error( $remote ) || 200 !== wp_remote_retrieve_response_code( $remote ) || empty( wp_remote_retrieve_body( $remote ) ) ) {
                error_log( print_r( wp_remote_retrieve_response_code( $remote ), true ) ); // phpcs:ignore
                return false;
            }

            set_transient( $this->cache_key, $remote, DAY_IN_SECONDS );
        }

        return json_decode( wp_remote_retrieve_body( $remote ) );
    }

    /**
     * Sets plugin info from the remote request.
     *
     * @param false|object|array $response The response object or array.
     * @param string             $action   The type of information being requested from the Plugin Installation API.
     * @param object             $args     Plugin API arguments.
     *
     * @return false|object|array
     */
    public function info( false|object|array $response, string $action, object $args ): false|object|array {
        // Do nothing if you're not getting plugin information right now.
        if ( 'plugin_information' !== $action ) {
            return $response;
        }

        // Do nothing if it is not our plugin.
        if ( empty( $args->slug ) || $this->plugin_slug !== $args->slug ) {
            return $response;
        }

        // Get updates.
        $remote = $this->request();

        if ( ! $remote ) {
            return $response;
        }

        $response                 = new \stdClass();
        $response->name           = $remote->name;
        $response->slug           = $remote->slug;
        $response->version        = $remote->version;
        $response->tested         = $remote->tested;
        $response->requires       = $remote->requires;
        $response->author         = $remote->author;
        $response->homepage       = $remote->homepage;
        $response->download_link  = $remote->download_url;
        $response->trunk          = $remote->download_url;
        $response->requires_php   = $remote->requires_php;
        $response->last_updated   = $remote->last_updated;

        $response->sections = [
            'description'  => $remote->sections->description,
            'installation' => $remote->sections->installation,
            'changelog'    => $remote->sections->changelog
        ];

        if ( ! empty( $remote->banners ) ) {
            $response->banners = [
                'low'  => $remote->banners->low,
                'high' => $remote->banners->high
            ];
        }

        return $response;
    }

    /**
     * Just clean the cache when new plugin version is installed.
     */
    public function update( $transient ) {
        if ( empty( $transient->checked ) ) {
            return $transient;
        }

        $remote = $this->request();

        if ( $remote && version_compare( $this->version, $remote->version, '<' ) && version_compare( $remote->requires, get_bloginfo( 'version' ), '<=' ) && version_compare( $remote->requires_php, PHP_VERSION, '<' ) ) {
            $response              = new \stdClass();
            $response->slug        = $this->plugin_slug;
            $response->plugin      = "{$this->plugin_slug}/{$this->plugin_slug}.php";
            $response->new_version = $remote->version;
            $response->tested      = $remote->tested;
            $response->package     = $remote->download_url;
            if ( ! empty( $remote->banners ) ) {
                $response->banners = [
                    'low'  => $remote->banners->low,
                    'high' => $remote->banners->high
                ];
            }

            $transient->response[ $response->plugin ] = $response;
        }

        return $transient;
    }

    /**
     * Just clean the cache when new plugin version is installed.
     *
     * @param \WP_Upgrader|\Plugin_Upgrader $upgrader WP_Upgrader instance.
     * @param array                         $options  Item update data.
     *
     * @return void
     */
    public function purge( \WP_Upgrader|\Plugin_Upgrader $upgrader, array $options ): void {
        if ( $this->cache_allowed && 'update' === $options['action'] && 'plugin' === $options[ 'type' ] ) {
            delete_transient( $this->cache_key );
        }
    }
}
