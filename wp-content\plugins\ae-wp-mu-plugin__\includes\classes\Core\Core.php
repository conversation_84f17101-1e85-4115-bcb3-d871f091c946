<?php
/**
 * American Eagle WP MU Plugin Core Functionality
 *
 * @package AmericaneagleWpMUPlugin\Core
 */

namespace AmericaneagleWpMUPlugin\Core;

use AmericaneagleWpMUPlugin\BaseAbstract;
use WP_Error;

/**
 * WordPress Core Functionality.
 */
class Core extends BaseAbstract {

	/**
	 * Activate the plugin
	 *
	 * @return void
	 */
	public function activation_hook(): void {
		flush_rewrite_rules();
		do_action( 'americaneagle_wp_mu_plugin_activated' );
	}

	/**
	 * Deactivate the plugin
	 *
	 * Uninstall routines should be in uninstall.php
	 *
	 * @return void
	 */
	public function deactivation_hook(): void {
		flush_rewrite_rules();
		do_action( 'americaneagle_wp_mu_plugin_deactivated' );
	}


	/**
	 * Registers the default textdomain.
	 *
	 * @return void
	 */
	public function i18n(): void {
		$locale = apply_filters( 'plugin_locale', get_locale(), 'americaneagle-wp-mu-plugin' );
		load_textdomain( 'americaneagle-wp-mu-plugin', WP_LANG_DIR . '/americaneagle-wp-mu-plugin/americaneagle-wp-mu-plugin-' . $locale . '.mo' );
		load_plugin_textdomain( 'americaneagle-wp-mu-plugin', false, plugin_basename( AMERICANEAGLE_WP_MU_PLUGIN_PATH ) . '/languages/' );
	}
}
