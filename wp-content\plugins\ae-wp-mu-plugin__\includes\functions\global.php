<?php
/**
 * Expose Global Functions from plugin functionality.
 *
 * @package AmericaneagleWpMUPlugin
 */

/**
 * Test to find out if this is an AE user.
 * Currently the logic is set by the email address.
 *
 * @return bool
 */
function is_ae_user(): bool {
	if ( is_user_logged_in() ) {
		$current_user = wp_get_current_user();
		$email        = $current_user->user_email;

		if ( empty( $email ) ) {
			return false;
		}

		$is_ae_user = preg_match( '/@americaneagle\.com/', $email );

		if ( $is_ae_user ) {
			return true;
		}
	}

	return false;
}
