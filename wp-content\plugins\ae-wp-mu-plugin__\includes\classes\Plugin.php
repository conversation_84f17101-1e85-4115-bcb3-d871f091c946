<?php
/**
 * American Eagle WP MU Plugin
 *
 * @package AmericaneagleWpMUPlugin
 */

namespace AmericaneagleWpMUPlugin;

use AmericaneagleWpMUPlugin\Admin\{Dashboard, Login, Plugins, SamlSso};
use AmericaneagleWpMUPlugin\Core\Core;
use AmericaneagleWpMUPlugin\Cron\PluginChecker;
use AmericaneagleWpMUPlugin\Events\PluginRemoval;
use AmericaneagleWpMUPlugin\Notifications\Email;
use AmericaneagleWpMUPlugin\Updater\AeWpMuUpdater;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

/**
 * American Eagle WP MU Plugin main class.
 */
class Plugin extends BaseAbstract {

	/**
	 * Set up the container deps and fire off all WordPress hooks used in this plugin.
	 */
	public function register(): void {
		try {
			$this->activation_hooks();
			$this->actions();
			$this->filters();
		} catch ( \Exception $e ) {
			echo esc_html( $e->getMessage() );
		}
	}

	/**
	 * Plugin activation and deactivation hooks.
	 *
	 * @return void
	 */
	private function activation_hooks(): void {
		register_activation_hook( __FILE__, [ $this->app->get( Core::class ), 'activation_hook' ] );
		register_deactivation_hook( __FILE__, [ $this->app->get( Core::class ), 'deactivation_hook' ] );
	}

	/**
	 * Invoke all add_action, remove_action, do_action hooks in here.
	 *
	 * @return void
	 * @throws ContainerExceptionInterface On container error.
	 * @throws NotFoundExceptionInterface On container not find class error.
	 */
	private function actions(): void {
		add_action( 'init', [ $this->app->get( Core::class ), 'i18n' ] );

		// Display dashboard widget if there are blacklisted plugins installed.
		add_action( 'wp_dashboard_setup', [ $this->app->get( Plugins::class ), 'blacklisted_plugins_notice' ] );

		// Prevent blacklisted plugins from being activated.
		add_action( 'activate_plugin', [ $this->app->get( Plugins::class ), 'block_blacklisted_plugins' ], 10, 2 );
		add_action( 'admin_notices', [ $this->app->get( Plugins::class ), 'blocked_plugin_activation_notice' ] );
		add_action( 'network_admin_notices', [ $this->app->get( Plugins::class ), 'blocked_plugin_activation_notice' ] );

		// Create cron to check plugins.
		add_action( 'init', [ $this->app->get( PluginChecker::class ), 'register' ] );

		// Deletion of blacklisted plugins.
		add_action( 'delete_blacklisted_plugins', [ $this->app->get( PluginRemoval::class ), 'deletes_plugins' ] );

		// Email notification of plugin deletion of blacklisted plugins.
		add_action( 'email_blacklisted_plugins_0', [ $this->app->get( Email::class ), 'send_notification_email' ], 10, 1 );
		add_action( 'email_blacklisted_plugins_5', [ $this->app->get( Email::class ), 'send_notification_email' ], 10, 1 );
		add_action( 'email_blacklisted_plugins_6', [ $this->app->get( Email::class ), 'send_notification_email' ], 10, 1 );

		// Hide admin pages and widgets.
		if ( is_multisite() ) {
			add_action( 'network_admin_menu', [ $this->app->get( Dashboard::class ), 'hide_menu_pages' ], 999 );
			add_action( 'wp_network_dashboard_setup', [ $this->app->get( Dashboard::class ), 'hide_dashboard_widgets' ], 999 );
		} else {
			add_action( 'admin_menu', [ $this->app->get( Dashboard::class ), 'hide_menu_pages' ], 999 );
			add_action( 'wp_dashboard_setup', [ $this->app->get( Dashboard::class ), 'hide_dashboard_widgets' ], 999 );
		}

		// AE Login.
		add_action( 'login_enqueue_scripts', [ $this->app->get( Login::class ), 'ae_login_page' ] );

		// SSO Saml config.
		// Hook into admin_init to check and install wp-simple-saml if necessary.
		add_action( 'admin_init', [ $this->app->get( SamlSso::class ), 'auto_install_wp_simple_saml' ] );

		// SAML SSO Login.
		add_action( 'login_enqueue_scripts', [ $this->app->get( SamlSso::class ), 'saml_ae_login_page' ] );

        // Registers plugin updater hooks.
        add_action( 'upgrader_process_complete', [ $this->app->get( AeWpMuUpdater::class ), 'purge' ], 10, 2 );

		do_action( 'americaneagle_wp_mu_plugin_loaded' );
	}

	/**
	 * Invoke all add_filter, remove_filter, do_filter hooks in here.
	 *
	 * @return void
	 * @throws ContainerExceptionInterface On container error.
	 * @throws NotFoundExceptionInterface On container not find class error.
	 */
	private function filters(): void {
		// Hide plugins.
		add_filter( 'all_plugins', [ $this->app->get( Plugins::class ), 'hide_plugins' ] );
		add_filter( 'views_plugins', [ $this->app->get( Plugins::class ), 'hide_mu_plugins' ] );

		// SSO Saml config.
		// SAML metadata XML file path.
		add_filter( 'wpsimplesaml_idp_metadata_xml_path', [ $this->app->get( SamlSso::class ), 'get_sso_saml' ] );

		// Configure attribute mapping between WordPress and SSO IdP SAML attributes.
		add_filter( 'wpsimplesaml_attribute_mapping', [ $this->app->get( SamlSso::class ), 'configure_saml_attr' ] );

		// Configure attribute mapping between WordPress and SSO IdP SAML attributes.
		add_filter( 'wpsimplesaml_manage_roles', '__return_true' );

        // Configure user role management.
        add_filter( 'wpsimplesaml_map_role', [ $this->app->get( SamlSso::class ), 'map_saml_roles' ] );

        // Registers filters for plugin update in dev mode.
        if ( defined( 'AE_WP_MU_PLUGIN_DEV_MODE' ) ) {
            add_filter( 'https_ssl_verify', '__return_false' );
            add_filter( 'https_local_ssl_verify', '__return_false' );
            add_filter( 'http_request_host_is_external', '__return_true' );
        }

        // Registers plugin updater hooks.
        add_filter( 'plugins_api', [ $this->app->get( AeWpMuUpdater::class ), 'info' ], 20, 3 );
        add_filter( 'site_transient_update_plugins', [ $this->app->get( AeWpMuUpdater::class ), 'update' ] );
	}
}
