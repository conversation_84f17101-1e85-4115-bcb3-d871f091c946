<?php
/**
 * American Eagle WP MU Plugin Plugins SAML SSO Class
 *
 * @package AmericaneagleWpMUPlugin\Admin
 */

namespace AmericaneagleWpMUPlugin\Admin;

use AmericaneagleWpMUPlugin\BaseAbstract;

/**
 * WordPress SamlSso Functionality.
 */
class SamlSso extends BaseAbstract {

	/**
	 * Auto install WP Simple SAML.
	 *
	 * @return void
	 */
	public function auto_install_wp_simple_saml(): void {
		// Check if wp-simple-saml is already installed and active.
		if ( ! array_key_exists( 'wp-simple-saml/plugin.php', get_plugins() ) ) {
			// Include necessary files for handling filesystem in WordPress.
			include_once ABSPATH . 'wp-admin/includes/plugin-install.php';
			include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';
			include_once ABSPATH . 'wp-admin/includes/class-plugin-upgrader.php';
        
			// Prepare plugin details.
			$plugin_slug   = 'wp-simple-saml';
			$plugin_folder = WP_PLUGIN_DIR . '/' . $plugin_slug;

			// Check if wp-simple-saml folder exists in plugins directory.
			if ( ! file_exists( $plugin_folder ) ) {
				// Prepare plugin installation from GitHub release.
				$plugin_zip_url = 'https://aepackagist.wpengine.com/releases/wp-simple-saml.zip';

				// Hook into the upgrader_package_options filter to adjust the installation destination.
				add_filter( 'upgrader_package_options', [ $this, 'customize_plugin_install_destination' ] );
        
				// Install plugin using WordPress plugin installer.
				$upgrader       = new \Plugin_Upgrader( new \WP_Upgrader_Skin() );
				$install_result = $upgrader->install( $plugin_zip_url );

				// Remove the filter after installation.
				remove_filter( 'upgrader_package_options', [ $this, 'customize_plugin_install_destination' ] );

				if ( is_wp_error( $install_result ) ) {
					// Handle installation error.
					error_log( 'Error installing wp-simple-saml plugin: ' . $install_result->get_error_message() );
					return;
				}
        
				// Activate the plugin after successful installation.
				$plugin_path = $plugin_folder . '/plugin.php';

				activate_plugin( $plugin_path );

				// Check if plugin is activated.
				if ( ! is_plugin_active( $plugin_path ) ) {
					// Handle activation failure.
					error_log( 'Error activating wp-simple-saml plugin.' );
					return;
				}

				// Optionally log activation success.
				error_log( 'wp-simple-saml plugin has been installed and activated successfully.' );
			}
		} else {
			// Ensure the plugin is active if it's loaded but not active.
			if ( ! is_plugin_active( 'wp-simple-saml/plugin.php' ) ) {
				activate_plugin( 'wp-simple-saml/plugin.php' );

				// Optionally log activation success.
				error_log( 'wp-simple-saml plugin has been activated successfully.' );
			}
		}
	}

	/**
	 * Customize plugin install DIR.
	 *
	 * @param array $options Options.
	 *
	 * @return array
	 */
	public function customize_plugin_install_destination( array $options ): array {
		$plugin_slug            = 'wp-simple-saml';
		$plugin_folder          = WP_PLUGIN_DIR . '/' . $plugin_slug;
		$options['destination'] = $plugin_folder;

		return $options;
	}

	/**
	 * Use Simple SAML SSO.
	 *
	 * @return array
	 */
	public function saml_ae_login_page(): void { 
		?>
		<style type="text/css">
			#login-via-sso {
				background:  #fff !important;
				border-color: rgba(255,255,255,0) !important;
			}

			#login-via-sso > img {
				width: 260px !important;
				height: 50px !important;
			}
		</style>
		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
		<script type="text/javascript">
			jQuery(document).ready(function() {
				document.querySelector("#login-via-sso").innerHTML = '<img src="https://ameagle-assets.azureedge.net/aecom-blobs/images/default-source/default-album/logo77462a6763c4412b9e51c1748903cc85.png">';
			});
		</script>
		<?php 
	}

	/**
	 * SAML metadata XML file path.
	 *
	 * @return string
	 */
	public function get_sso_saml(): string {
		return WPMU_PLUGIN_DIR . '/ae-sso/sso.xml';
	}

	/**
	 * Configure attribute mapping between WordPress and SSO IdP SAML attributes.
	 *
	 * @return array
	 */
	public function configure_saml_attr(): array {
		return [
			'user_login' => 'uid',
			'user_email' => 'email',
		];
	}

	/**
	 * Map SAML roles.
	 *
	 * @return array
	 */
	public function map_saml_roles(): array {
		return [
			'user_roles' => 'administrator','americaneagle',
		];
	}
}
