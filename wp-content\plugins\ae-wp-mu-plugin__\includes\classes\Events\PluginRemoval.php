<?php
/**
 * American Eagle WP MU Plugin Schedule Plugin Removal Class
 *
 * @package AmericaneagleWpMUPlugin\Events
 */

namespace AmericaneagleWpMUPlugin\Events;

use AmericaneagleWpMUPlugin\BaseAbstract;

/**
 * WordPress PluginRemoval Functionality.
 */
class PluginRemoval extends BaseAbstract {

	/**
	 * Deletes plugins.
	 *
	 * @return void
	 */
	public function deletes_plugins(): void {
		if ( ! function_exists( 'request_filesystem_credentials' ) ) {
			require_once( ABSPATH . 'wp-admin/includes/file.php' );
		}

		// Check to see what plugins are still installed and require deletion.
		$current_plugins = get_plugins();

		foreach( self::BLACKLISTED_PLUGINS as $blocked_plugin ) {
			if ( isset( $current_plugins[ $blocked_plugin ] ) ) {
				if ( is_plugin_active( $blocked_plugin ) ) {
					deactivate_plugins( $blocked_plugin, true );
				}
	
				if ( isset( get_plugins()[ $blocked_plugin ] ) ) {
					delete_plugins( [ $blocked_plugin ] );
				}
			}
		}
	}
}
