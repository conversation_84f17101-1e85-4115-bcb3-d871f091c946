const Icons = {
  smartphone: <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
      <path fill="none" d="M0 0H20V20H0z"></path>
      <path d="M6 2h8c.55 0 1 .45 1 1v14c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1zm7 12V4H7v10h6zM8 5h4l-4 5V5z"></path>
    </svg>,
    desktop: <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
    <path fill="none" d="M0 0H20V20H0z"></path>
    <path d="M3 2h14c.55 0 1 .45 1 1v10c0 .55-.45 1-1 1h-5v2h2c.55 0 1 .45 1 1v1H5v-1c0-.55.45-1 1-1h2v-2H3c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1zm13 9V4H4v7h12zM5 5h9L5 9V5z"></path>
  </svg>,
  tablet: <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
  <path fill="none" d="M0 0H20V20H0z"></path>
  <path d="M4 2h12c.55 0 1 .45 1 1v14c0 .55-.45 1-1 1H4c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1zm11 14V4H5v12h10zM6 5h6l-6 5V5z"></path>
</svg>,
globe: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<path fill="none" d="M0 0H20V20H0z"></path>
<path
  fillRule="nonzero"
  d="M10 1a9 9 0 10.001 18.001A9 9 0 0010 1zm3.46 11.95c0 1.47-.8 3.3-4.06 4.7.3-4.17-2.52-3.69-3.2-5A3.25 3.25 0 018 10.1c-1.552-.266-3-.96-4.18-2 .05.47.28.904.64 1.21a4.18 4.18 0 01-1.94-1.5 7.94 7.94 0 017.25-5.63c-.84 1.38-1.5 4.13 0 5.57C8.23 8 7.26 6 6.41 6.79c-1.13 1.06.33 2.51 3.42 3.08 3.29.59 3.66 1.58 3.63 3.08zm1.34-4c-.32-1.11.62-2.23 1.69-3.14a7.27 7.27 0 01.84 6.68c-.77-1.89-2.17-2.32-2.53-3.57v.03z"
></path>
</svg>,
generic: <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
<path fill="none" d="M0 0H20V20H0z"></path>
<path d="M18 12h-2.18c-.17.7-.44 1.35-.81 1.93l1.54 1.54-2.1 2.1-1.54-1.54c-.58.36-1.23.63-1.91.79V19H8v-2.18c-.68-.16-1.33-.43-1.91-.79l-1.54 1.54-2.12-2.12 1.54-1.54c-.36-.58-.63-1.23-.79-1.91H1V9.03h2.17c.16-.7.44-1.35.8-1.94L2.43 5.55l2.1-2.1 1.54 1.54c.58-.37 1.24-.64 1.93-.81V2h3v2.18c.68.16 1.33.43 1.91.79l1.54-1.54 2.12 2.12-1.54 1.54c.36.59.64 1.24.8 1.94H18V12zm-8.5 1.5c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"></path>
</svg>,
	logo: <svg
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 50 30"
  >
		<path d="M24.23 9.074a.793.793 0 10-.001-1.585.793.793 0 00.001 1.585M26.336 7.551a.915.915 0 100-1.83.915.915 0 000 1.83M27.524 22.051h-5.055a.701.701 0 01-.577-1.1l2.586-3.743a.701.701 0 011.162.012l2.47 3.743a.702.702 0 01-.586 1.088m3.077-.962l-3.594-5.936v-3.315h.088a.941.941 0 000-1.884h-4.189a.942.942 0 000 1.884h.087v3.315l-3.594 5.936a1.901 1.901 0 001.703 2.749h7.796a1.902 1.902 0 001.703-2.749"></path>
	  <path d="M35.058.79l.***************.**************.***************.**************.***************.**********.***************.**************.***************.***************.***************.***************.**************.***************.***************.***************.***************.***************.**************.***************.***************.***************.***************.**************.***************.**********.***************.**************.***************.**************.***************.001.11v19.896l-.001.11-.004.109-.007.109-.01.108-.012.107-.015.106-.017.105-.02.105-.023.103-.025.103-.027.102-.03.1-.033.1-.034.098-.037.098-.04.096-.041.095-.044.094-.046.092-.048.092-.051.089-.052.089-.055.087-.056.086-.059.084-.06.083-.062.082-.065.079-.066.079-.068.076-.069.075-.072.073-.073.072-.075.069-.076.068-.079.066-.079.065-.082.062-.083.06-.084.059-.086.056-.087.055-.089.052-.089.051-.092.048-.092.046-.094.044-.095.041-.096.04-.098.037-.098.034-.1.033-.1.03-.102.027-.103.025-.103.023-.105.02-.105.017-.106.015-.107.012-.108.01-.109.007-.109.004-.11.001H15.052l-.11-.001-.109-.004-.109-.007-.108-.01-.107-.012-.106-.015-.105-.017-.105-.02-.103-.023-.103-.025-.102-.027-.1-.03-.1-.033-.098-.034-.098-.037-.096-.04-.095-.041-.094-.044-.092-.046-.092-.048-.089-.051-.089-.052-.087-.055-.086-.056-.084-.059-.083-.06-.082-.062-.079-.065-.079-.066-.076-.068-.075-.069-.073-.072-.072-.073-.069-.075-.068-.076-.066-.079-.065-.079-.062-.082-.06-.083-.059-.084-.056-.086-.055-.087-.052-.089-.051-.089-.048-.092-.046-.092-.044-.094-.041-.095-.04-.096-.037-.098-.034-.098-.033-.1-.03-.1-.027-.102-.025-.103-.023-.103-.02-.105-.017-.105-.015-.106-.012-.107-.01-.108-.007-.109-.004-.109-.001-.11V5.052l.001-.11.004-.109.007-.109.01-.108.012-.107.015-.106.017-.105.02-.105.023-.103.025-.103.027-.102.03-.1.033-.1.034-.098.037-.098.04-.096.041-.095.044-.094.046-.092.048-.092.051-.089.052-.089.055-.087.056-.086.059-.084.06-.083.062-.082.065-.079.066-.079.068-.076.069-.075.072-.073.073-.072.075-.069.076-.068.079-.066.079-.065.082-.062.083-.06.084-.059.086-.056.087-.055.089-.052.089-.051.092-.048.092-.046.094-.044.095-.041.096-.04.098-.037.098-.034.1-.033.1-.03.102-.027.103-.025.103-.023.105-.02.105-.017.106-.015.107-.012.108-.01.109-.007.109-.004.11-.001h19.896l.11.001zM15.061 2.289l-.081.001-.071.002-.071.005-.07.006-.069.008-.069.01-.068.011-.068.013-.067.014-.066.017-.065.017-.065.02-.065.021-.063.022-.063.024-.062.025-.062.027-.06.028-.06.03-.059.031-.058.033-.058.034-.056.035-.056.037-.055.038-.054.039-.053.041-.051.041-.051.043-.05.045-.049.045-.047.047-.047.047-.045.049-.045.05-.043.051-.041.051-.041.053-.039.054-.038.055-.037.056-.035.056-.034.058-.033.058-.031.059-.03.06-.028.06-.027.062-.025.062-.024.063-.022.063-.021.065-.02.065-.017.065-.017.066-.014.067-.013.068-.011.068-.01.069-.008.069-.006.07-.005.071-.002.071-.001.081v19.878l.001.081.002.071.005.071.006.07.008.069.01.069.011.068.013.068.014.067.017.066.017.065.02.065.021.065.022.063.024.063.025.062.027.062.028.06.03.06.031.059.033.058.034.058.035.056.037.056.038.055.039.054.041.053.042.051.042.051.045.05.045.049.047.047.047.047.049.045.05.045.051.043.051.041.053.041.054.039.055.038.056.037.056.035.058.034.058.033.059.031.06.03.06.028.062.027.062.025.063.024.063.022.065.021.065.02.065.017.066.017.067.014.068.013.068.011.069.01.069.008.07.006.071.005.071.002.081.001h19.878l.081-.001.071-.002.071-.005.07-.006.069-.008.069-.01.068-.011.068-.013.067-.014.066-.017.065-.017.065-.02.065-.021.063-.022.063-.024.062-.025.062-.027.06-.028.06-.03.059-.031.058-.033.058-.034.056-.035.056-.037.055-.038.054-.039.053-.041.051-.042.051-.042.05-.045.049-.045.047-.047.047-.047.045-.049.045-.05.042-.051.042-.051.041-.053.039-.054.038-.055.037-.056.035-.056.034-.058.033-.058.031-.059.03-.06.028-.06.027-.062.025-.062.024-.063.022-.063.021-.065.02-.065.017-.065.017-.066.014-.067.013-.068.011-.068.01-.069.008-.069.006-.07.005-.071.002-.071.001-.081V5.061l-.001-.081-.002-.071-.005-.071-.006-.07-.008-.069-.01-.069-.011-.068-.013-.068-.014-.067-.017-.066-.017-.065-.02-.065-.021-.065-.022-.063-.024-.063-.025-.062-.027-.062-.028-.06-.03-.06-.031-.059-.033-.058-.034-.058-.035-.056-.037-.056-.038-.055-.039-.054-.041-.053-.041-.051-.043-.051-.045-.05-.045-.049-.047-.047-.047-.047-.049-.045-.05-.045-.051-.042-.051-.042-.053-.041-.054-.039-.055-.038-.056-.037-.056-.035-.058-.034-.058-.033-.059-.031-.06-.03-.06-.028-.062-.027-.062-.025-.063-.024-.063-.022-.065-.021-.065-.02-.065-.017-.066-.017-.067-.014-.068-.013-.068-.011-.069-.01-.069-.008-.07-.006-.071-.005-.071-.002-.081-.001H15.061z"></path>
  </svg>,
	logoTitleTag: <svg
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 50 30"
  >
		<path d="M9.984 10.805a.56.56 0 100-*********** 0 000 1.122M11.475 9.727a.648.648 0 100-1.295.648.648 0 000 1.295M12.316 19.991H8.737c-.4 0-.636-.45-.408-.779l1.831-2.649a.496.496 0 01.822.009l1.748 2.649a.496.496 0 01-.414.77m2.178-.681l-2.544-4.202v-2.346h.062a.667.667 0 000-1.333H9.047a.667.667 0 100 1.333h.061v2.346L6.565 19.31a1.346 1.346 0 001.205 1.946h5.518c1 0 1.651-1.051 1.206-1.946"></path>
	  <path d="M17.649 4.942l.***************.***************.***************.***************.***************.**************.**************.***************.***************.***************.***************.************.***************.***************.***************.***************.***************.***************.***************.************.***************.***************.***************.***************.**************.***************.***************.***************.**************.***************.***************.001.078v14.082l-.001.078-.002.078-.005.077-.007.076-.009.076-.01.075-.013.074-.014.074-.016.074-.018.072-.019.072-.021.071-.023.071-.025.07-.026.069-.028.068-.029.067-.031.066-.033.066-.034.064-.036.064-.037.063-.038.062-.04.06-.042.06-.042.059-.045.057-.045.057-.047.055-.048.054-.049.053-.051.052-.052.051-.053.049-.054.048-.055.047-.057.045-.057.044-.059.043-.06.042-.06.04-.062.038-.063.037-.063.036-.065.034-.065.032-.067.032-.067.029-.068.028-.069.026-.07.025-.07.022-.072.022-.072.019-.072.018-.073.016-.074.014-.075.012-.075.011-.076.009-.076.006-.077.005-.077.003-.078.001H3.488l-.078-.001-.077-.003-.077-.005-.077-.006-.075-.009-.076-.011-.074-.012-.074-.014-.073-.016-.073-.018-.072-.019-.071-.022-.071-.022-.069-.025-.069-.026-.068-.028-.068-.029-.066-.032-.066-.032-.064-.034-.064-.036-.063-.037-.061-.038-.061-.04-.06-.042-.058-.043-.058-.044-.056-.045-.056-.047-.054-.048-.053-.049-.052-.051-.05-.052-.05-.053-.048-.054-.047-.055-.045-.057-.044-.057-.043-.059-.041-.06-.04-.06-.039-.062-.037-.063-.036-.064-.034-.064-.032-.066-.031-.066-.03-.067-.028-.068-.026-.069-.024-.07-.023-.071-.022-.071-.019-.072-.018-.072-.016-.074-.014-.074-.012-.074-.011-.075-.008-.076-.007-.076-.005-.077-.003-.078-.001-.078V7.959l.001-.078.003-.078.005-.077.007-.076.008-.076.011-.075.012-.074.014-.074.016-.074.018-.072.019-.072.022-.071.023-.071.024-.07.026-.069.028-.068.03-.067.031-.066.032-.066.034-.064.036-.064.037-.063.039-.062.04-.06.041-.06.043-.059.044-.057.045-.057.047-.055.048-.054.05-.053.05-.052.052-.051.053-.049.054-.048.056-.047.056-.045.058-.044.058-.043.06-.042.061-.04.061-.038.063-.037.064-.036.064-.034.066-.032.066-.032.068-.029.068-.028.069-.026.069-.025.071-.022.071-.022.072-.019.073-.018.073-.016.074-.014.074-.012.076-.011.075-.009.077-.006.077-.005.077-.003.078-.001h14.083l.078.001zM3.497 6.441h-.049l-.039.002-.039.002-.039.004-.038.004-.038.005-.037.007-.037.007-.037.008-.036.009-.036.009-.035.011-.036.011-.034.013-.035.013-.034.014-.033.014-.034.016-.032.016-.033.017-.032.018-.031.019-.032.019-.03.02-.03.021-.03.022-.029.022-.028.023-.029.024-.027.024-.027.025-.026.026-.026.026-.025.027-.024.027-.024.028-.023.029-.022.029-.022.03-.02.03-.021.03-.019.031-.019.032-.018.032-.017.032-.016.033-.016.033-.014.034-.014.034-.013.034-.013.035-.011.035-.011.036-.009.036-.009.036-.008.037-.007.037-.006.037-.006.038-.004.038-.003.038-.003.039-.001.04-.001.049v14.064l.001.049.001.04.003.039.003.038.004.038.006.038.006.037.007.037.008.037.009.036.009.036.011.036.011.035.013.035.013.034.014.034.014.034.016.033.016.033.017.032.018.032.019.032.019.031.021.03.02.03.022.03.022.029.023.029.024.028.024.027.025.027.026.026.026.026.027.025.028.024.028.024.028.023.029.022.03.022.03.021.03.02.032.019.031.019.032.018.033.017.032.016.034.016.033.014.034.014.035.013.034.013.036.011.035.011.036.009.036.009.037.008.037.007.037.007.038.005.038.004.039.004.039.002.039.002H17.61l.04-.002.039-.002.038-.004.038-.004.038-.005.038-.007.037-.007.036-.008.036-.009.036-.009.036-.011.035-.011.035-.013.034-.013.034-.014.034-.014.033-.016.033-.016.032-.017.032-.018.032-.019.031-.019.03-.02.031-.021.029-.022.029-.022.029-.023.028-.024.027-.024.027-.025.026-.026.026-.026.025-.027.024-.027.024-.028.023-.029.022-.029.022-.03.021-.03.02-.03.019-.031.019-.032.018-.032.017-.032.016-.033.016-.033.015-.034.014-.034.013-.034.012-.035.011-.035.011-.036.01-.036.009-.036.007-.037.008-.037.006-.037.005-.038.004-.038.004-.038.002-.039.002-.04V7.919l-.002-.04-.002-.039-.004-.038-.004-.038-.005-.038-.006-.037-.008-.037-.007-.037-.009-.036-.01-.036-.011-.036-.011-.035-.012-.035-.013-.034-.014-.034-.015-.034-.016-.033-.016-.033-.017-.032-.018-.032-.019-.032-.019-.031-.02-.03-.021-.03-.022-.03-.022-.029-.023-.029-.024-.028-.024-.027-.025-.027-.026-.026-.026-.026-.027-.025-.027-.024-.028-.024-.029-.023-.029-.022-.029-.022-.031-.021-.03-.02-.031-.019-.032-.019-.032-.018-.032-.017-.033-.016-.033-.016-.034-.014-.034-.014-.034-.013-.035-.013-.035-.011-.036-.011-.036-.009-.036-.009-.036-.008-.037-.007-.038-.007-.038-.005-.038-.004-.038-.004-.039-.002-.04-.002H3.497z"></path>
	  <path d="M27.867 8.104V9.47h-2.146v5.749h-1.602V9.47h-2.146V8.104h5.894z"></path>
	  <path d="M28.893 8.104H30.495V15.219H28.893z"></path>
	  <path d="M37.415 8.104V9.47H35.27v5.749h-1.602V9.47h-2.146V8.104h5.893zM43.196 13.844v1.375h-4.754V8.104h1.602v5.74h3.152zM44.223 15.219V8.104h4.805v1.345h-3.204v1.397h2.844v1.314h-2.844v1.714h3.44v1.345h-5.041z"></path>
		<path d="M25.779 17.216v.881h-1.385v3.713H23.36v-3.713h-1.386v-.881h3.805zM28.975 21.81a11.73 11.73 0 00-.163-.491c-.059-.168-.118-.336-.175-.504h-1.79c-.058.168-.116.336-.176.504-.06.168-.114.332-.162.491h-1.074c.172-.495.336-.953.49-1.373.155-.419.306-.815.454-1.186.148-.371.294-.724.438-1.057.143-.334.293-.66.447-.978h.988a37.492 37.492 0 011.339 3.221c.155.42.318.878.491 1.373h-1.107zm-1.24-3.554a6.44 6.44 0 01-.099.272l-.153.398-.189.497c-.068.181-.138.371-.209.57h1.306a26.56 26.56 0 00-.394-1.067c-.06-.15-.112-.283-.156-.398a17.567 17.567 0 00-.106-.272zM32.753 18.011c-.481 0-.83.134-1.044.401-.214.268-.321.633-.321 1.097 0 .226.026.43.079.614.053.183.133.341.239.474.106.132.238.235.398.308.159.073.344.109.556.109.115 0 .214-.002.295-.006.082-.005.154-.014.216-.027v-1.598h1.034v2.274a3.832 3.832 0 01-.597.156 5.256 5.256 0 01-1.014.083c-.345 0-.657-.053-.938-.159a1.961 1.961 0 01-.719-.464 2.073 2.073 0 01-.461-.749 2.953 2.953 0 01-.162-1.015c0-.384.059-.724.179-1.021.119-.296.283-.546.49-.752.208-.205.452-.361.733-.467.28-.106.58-.159.898-.159a3.606 3.606 0 011.037.142 2.132 2.132 0 01.488.209l-.299.829a2.586 2.586 0 00-.487-.196 2.077 2.077 0 00-.6-.083zM38.235 20.921v.889h-3.069v-4.594H36.2v3.705h2.035z"></path>
		<path d="M38.898 17.216H39.932V21.810000000000002H38.898z"></path>
		<path d="M44.076 21.81a23.18 23.18 0 00-.961-1.558 15.8 15.8 0 00-1.101-1.452v3.01h-1.021v-4.594h.842c.146.146.307.325.484.537a23.033 23.033 0 011.087 1.428c.181.259.352.507.511.746v-2.711h1.027v4.594h-.868zM46.005 21.81v-4.594h3.102v.868h-2.068v.902h1.836v.848h-1.836v1.107h2.221v.869h-3.255z"></path>
  </svg>,
	logoTitle: <svg
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 50 30"
  >
		<path d="M10.005 10.805a.56.56 0 100-*********** 0 000 1.122M11.496 9.727a.648.648 0 100-1.295.648.648 0 000 1.295M12.337 19.991H8.759a.497.497 0 01-.409-.779l1.831-2.649a.496.496 0 01.822.009l1.748 2.649a.496.496 0 01-.414.77m2.178-.681l-2.544-4.202v-2.346h.062a.667.667 0 000-1.333H9.068a.667.667 0 100 1.333h.062v2.346L6.586 19.31a1.346 1.346 0 001.205 1.946h5.518c1 0 1.651-1.051 1.206-1.946"></path>
	  <path d="M17.67 4.942l.***************.***************.***************.***************.***************.**************.**************.***************.***************.***************.***************.**************.***************.***************.***************.***************.***************.***************.***************.************.***************.***************.***************.***************.**************.***************.***************.***************.**************.***************.***************.001.078v14.082l-.001.078-.003.078-.005.077-.007.076-.009.076-.01.075-.013.074-.014.074-.016.074-.018.072-.019.072-.021.071-.023.071-.025.07-.026.069-.028.068-.029.067-.031.066-.033.066-.034.064-.036.064-.037.063-.038.062-.04.06-.042.06-.042.059-.044.057-.046.057-.047.055-.048.054-.049.053-.051.052-.052.051-.053.049-.054.048-.055.047-.057.045-.057.044-.059.043-.059.042-.061.04-.062.038-.063.037-.063.036-.065.034-.065.032-.067.032-.067.029-.068.028-.069.026-.07.025-.07.022-.071.022-.072.019-.073.018-.073.016-.074.014-.075.012-.075.011-.076.009-.076.006-.077.005-.077.003-.078.001H3.509l-.078-.001-.077-.003-.077-.005-.077-.006-.075-.009-.076-.011-.074-.012-.074-.014-.073-.016-.073-.018-.072-.019-.071-.022-.071-.022-.069-.025-.069-.026-.068-.028-.068-.029-.066-.032-.066-.032-.064-.034-.064-.036-.063-.037-.061-.038-.061-.04-.06-.042-.058-.043-.058-.044-.056-.045-.056-.047-.054-.048-.053-.049-.052-.051-.05-.052-.05-.053-.048-.054-.047-.055-.045-.057-.044-.057-.043-.059-.041-.06-.04-.06-.039-.062-.037-.063-.036-.064-.034-.064-.032-.066-.031-.066-.03-.067-.028-.068-.026-.069-.024-.07-.023-.071-.021-.071-.02-.072-.018-.072-.016-.074-.014-.074-.012-.074-.011-.075-.008-.076-.007-.076-.005-.077-.003-.078-.001-.078V7.959l.001-.078.003-.078.005-.077.007-.076.008-.076.011-.075.012-.074.014-.074.016-.074.018-.072.02-.072.021-.071.023-.071.024-.07.026-.069.028-.068.03-.067.031-.066.032-.066.034-.064.036-.064.037-.063.039-.062.04-.06.041-.06.043-.059.044-.057.045-.057.047-.055.048-.054.05-.053.05-.052.052-.051.053-.049.054-.048.056-.047.056-.045.058-.044.058-.043.06-.042.061-.04.061-.038.063-.037.064-.036.064-.034.066-.032.066-.032.068-.029.068-.028.069-.026.069-.025.071-.022.071-.022.072-.019.073-.018.073-.016.074-.014.074-.012.076-.011.075-.009.077-.006.077-.005.077-.003.078-.001h14.083l.078.001zM3.518 6.441h-.049l-.039.002-.039.002-.039.004-.038.004-.038.005-.037.007-.037.007-.037.008-.036.009-.036.009-.035.011-.035.011-.035.013-.035.013-.034.014-.033.014-.034.016-.032.016-.033.017-.032.018-.031.019-.031.019-.031.02-.03.021-.03.022-.029.022-.028.023-.028.024-.028.024-.027.025-.026.026-.026.026-.025.027-.024.027-.024.028-.023.029-.022.029-.022.03-.02.03-.021.03-.019.031-.019.032-.018.032-.017.032-.016.033-.015.033-.015.034-.014.034-.013.034-.012.035-.012.035-.01.036-.01.036-.009.036-.008.037-.007.037-.006.037-.006.038-.004.038-.003.038-.003.039-.001.04-.001.049v14.064l.001.049.001.04.003.039.003.038.004.038.006.038.006.037.007.037.008.037.009.036.01.036.01.036.012.035.012.035.013.034.014.034.015.034.015.033.016.033.017.032.018.032.019.032.019.031.021.03.02.03.022.03.022.029.023.029.024.028.024.027.025.027.026.026.026.026.027.025.028.024.028.024.028.023.029.022.03.022.03.021.031.02.031.019.031.019.032.018.033.017.032.016.034.016.033.014.034.014.035.013.035.013.035.011.035.011.036.009.036.009.037.008.037.007.037.007.038.005.038.004.039.004.039.002.039.002h14.163l.039-.002.039-.002.038-.004.039-.004.037-.005.038-.007.037-.007.036-.008.036-.009.036-.009.036-.011.035-.011.035-.013.034-.013.034-.014.034-.014.033-.016.033-.016.032-.017.032-.018.032-.019.031-.019.031-.02.03-.021.029-.022.029-.022.029-.023.028-.024.027-.024.027-.025.027-.026.025-.026.025-.027.024-.027.024-.028.023-.029.022-.029.022-.03.021-.03.02-.03.02-.031.018-.032.018-.032.017-.032.017-.033.015-.033.015-.034.014-.034.013-.034.012-.035.011-.035.011-.036.01-.036.009-.036.008-.037.007-.037.006-.037.005-.038.005-.038.003-.038.002-.039.002-.04.001-.049V7.968l-.001-.049-.002-.04-.002-.039-.003-.038-.005-.038-.005-.038-.006-.037-.007-.037-.008-.037-.009-.036-.01-.036-.011-.036-.011-.035-.012-.035-.013-.034-.014-.034-.015-.034-.015-.033-.017-.033-.017-.032-.018-.032-.018-.032-.02-.031-.02-.03-.021-.03-.022-.03-.022-.029-.023-.029-.024-.028-.024-.027-.025-.027-.025-.026-.027-.026-.027-.025-.027-.024-.028-.024-.029-.023-.029-.022-.029-.022-.03-.021-.031-.02-.031-.019-.032-.019-.032-.018-.032-.017-.033-.016-.033-.016-.034-.014-.034-.014-.034-.013-.035-.013-.035-.011-.036-.011-.036-.009-.036-.009-.036-.008-.037-.007-.038-.007-.037-.005-.039-.004-.038-.004-.039-.002-.039-.002H3.518z"></path>
	  <path d="M28.058 11.442v1.366h-2.146v5.75H24.31v-5.75h-2.145v-1.366h5.893z"></path>
	  <path d="M29.085 11.442H30.687V18.557000000000002H29.085z"></path>
	  <path d="M37.607 11.442v1.366h-2.146v5.75h-1.602v-5.75h-2.146v-1.366h5.894zM43.387 17.182v1.376h-4.754v-7.116h1.602v5.74h3.152zM44.414 18.558v-7.116h4.805v1.345h-3.203v1.397h2.844v1.314h-2.844v1.715h3.439v1.345h-5.041z"></path>
  </svg>,
	titleLogo: <svg
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 50 30"
  >
		<path d="M39.066 10.805a.56.56 0 10-.001-*********** 0 00.001 1.121M40.557 9.727a.647.647 0 100-1.295.647.647 0 000 1.295M41.398 19.991h-3.579c-.4 0-.636-.45-.408-.779l1.83-2.649a.497.497 0 01.823.009l1.748 2.649a.496.496 0 01-.414.77m2.177-.681l-2.543-4.202v-2.346h.061a.667.667 0 000-1.333h-2.965a.666.666 0 100 1.333h.062v2.346l-2.544 4.202a1.347 1.347 0 001.206 1.946h5.518c1 0 1.651-1.051 1.205-1.946"></path>
	  <path d="M46.73 4.942l.***************.***************.***************.***************.***************.***************.***************.***************.***************.***************.***************.*************.***************.***************.***************.**************.**************.***************.***************.************.***************.***************.***************.**************.**************.***************.**************.***************.***************.***************.***************.001.078v14.082l-.001.078-.003.078-.005.077-.007.076-.008.076-.011.075-.012.074-.014.074-.016.074-.018.072-.02.072-.021.071-.023.071-.024.07-.026.069-.028.068-.03.067-.031.066-.032.066-.034.064-.036.064-.037.063-.039.062-.04.06-.041.06-.043.059-.044.057-.045.057-.047.055-.048.054-.05.053-.05.052-.052.051-.053.049-.054.048-.056.047-.056.045-.058.044-.058.043-.06.042-.061.04-.061.038-.063.037-.064.036-.064.034-.066.032-.066.032-.068.029-.068.028-.069.026-.069.025-.071.022-.071.022-.072.019-.073.018-.073.016-.074.014-.074.012-.076.011-.075.009-.077.006-.077.005-.077.003-.078.001H32.569l-.078-.001-.077-.003-.077-.005-.076-.006-.076-.009-.075-.011-.075-.012-.074-.014-.073-.016-.073-.018-.071-.019-.072-.022-.07-.022-.07-.025-.069-.026-.068-.028-.067-.029-.067-.032-.065-.032-.065-.034-.063-.036-.063-.037-.062-.038-.06-.04-.06-.042-.059-.043-.057-.044-.057-.045-.055-.047-.054-.048-.053-.049-.052-.051-.051-.052-.049-.053-.048-.054-.047-.055-.045-.057-.045-.057-.042-.059-.042-.06-.04-.06-.038-.062-.037-.063-.036-.064-.034-.064-.033-.066-.031-.066-.029-.067-.028-.068-.026-.069-.025-.07-.023-.071-.021-.071-.019-.072-.018-.072-.016-.074-.014-.074-.013-.074-.01-.075-.009-.076-.007-.076-.005-.077-.002-.078-.001-.078V7.959l.001-.078.002-.078.005-.077.007-.076.009-.076.01-.075.013-.074.014-.074.016-.074.018-.072.019-.072.021-.071.023-.071.025-.07.026-.069.028-.068.029-.067.031-.066.033-.066.034-.064.036-.064.037-.063.038-.062.04-.06.042-.06.042-.059.045-.057.045-.057.047-.055.048-.054.049-.053.051-.052.052-.051.053-.049.054-.048.055-.047.057-.045.057-.044.059-.043.06-.042.06-.04.062-.038.063-.037.063-.036.065-.034.065-.032.067-.032.067-.029.068-.028.069-.026.07-.025.07-.022.072-.022.071-.019.073-.018.073-.016.074-.014.075-.012.075-.011.076-.009.076-.006.077-.005.077-.003.078-.001h14.083l.078.001zM32.579 6.441h-.05l-.039.002-.039.002-.038.004-.038.004-.038.005-.038.007-.037.007-.036.008-.036.009-.036.009-.036.011-.035.011-.035.013-.034.013-.034.014-.034.014-.033.016-.033.016-.032.017-.032.018-.032.019-.031.019-.031.02-.03.021-.029.022-.029.022-.029.023-.028.024-.027.024-.027.025-.026.026-.026.026-.025.027-.024.027-.024.028-.023.029-.022.029-.022.03-.021.03-.02.03-.019.031-.019.032-.018.032-.017.032-.016.033-.016.033-.015.034-.014.034-.013.034-.012.035-.011.035-.011.036-.01.036-.009.036-.008.037-.007.037-.006.037-.005.038-.004.038-.004.038-.002.039-.002.04V22.081l.002.04.002.039.004.038.004.038.005.038.006.037.007.037.008.037.009.036.01.036.011.036.011.035.012.035.013.034.014.034.015.034.016.033.016.033.017.032.018.032.019.032.019.031.02.03.021.03.022.03.022.029.023.029.024.028.024.027.025.027.026.026.026.026.027.025.027.024.028.024.029.023.029.022.029.022.03.021.031.02.031.019.032.019.032.018.032.017.033.016.033.016.034.014.034.014.034.013.035.013.035.011.036.011.036.009.036.009.036.008.037.007.038.007.038.005.038.004.038.004.039.002.039.002h14.163l.039-.002.039-.002.039-.004.038-.004.038-.005.037-.007.037-.007.037-.008.036-.009.036-.009.035-.011.036-.011.034-.013.035-.013.034-.014.033-.014.034-.016.032-.016.033-.017.032-.018.031-.019.031-.019.031-.02.03-.021.03-.022.029-.022.028-.023.028-.024.028-.024.027-.025.026-.026.026-.026.025-.027.024-.027.024-.028.023-.029.022-.029.022-.03.02-.03.021-.03.019-.031.019-.032.018-.032.017-.032.016-.033.016-.033.014-.034.014-.034.013-.034.013-.035.011-.035.011-.036.009-.036.009-.036.008-.037.007-.037.006-.037.006-.038.004-.038.003-.038.003-.039.001-.04.001-.049V7.968l-.001-.049-.001-.04-.003-.039-.003-.038-.004-.038-.006-.038-.006-.037-.007-.037-.008-.037-.009-.036-.009-.036-.011-.036-.011-.035-.013-.035-.013-.034-.014-.034-.014-.034-.016-.033-.016-.033-.017-.032-.018-.032-.019-.032-.019-.031-.021-.03-.02-.03-.022-.03-.022-.029-.023-.029-.024-.028-.024-.027-.025-.027-.026-.026-.026-.026-.027-.025-.028-.024-.028-.024-.028-.023-.029-.022-.03-.022-.03-.021-.031-.02-.031-.019-.031-.019-.032-.018-.033-.017-.032-.016-.034-.016-.033-.014-.034-.014-.035-.013-.034-.013-.036-.011-.035-.011-.036-.009-.036-.009-.037-.008-.037-.007-.037-.007-.038-.005-.038-.004-.039-.004-.039-.002-.039-.002H32.579z"></path>
	  <path d="M6.37 11.442v1.366H4.224v5.75H2.622v-5.75H.476v-1.366H6.37z"></path>
	  <path d="M7.396 11.442H8.998V18.557000000000002H7.396z"></path>
	  <path d="M15.918 11.442v1.366h-2.146v5.75h-1.601v-5.75h-2.146v-1.366h5.893zM21.699 17.182v1.376h-4.754v-7.116h1.602v5.74h3.152zM22.725 18.558v-7.116h4.805v1.345h-3.203v1.397h2.844v1.314h-2.844v1.715h3.44v1.345h-5.042z"></path>
  </svg>,
  	titleTagLogo: <svg
      xmlns="http://www.w3.org/2000/svg"
      fillRule="evenodd"
      strokeLinejoin="round"
      strokeMiterlimit="2"
      clipRule="evenodd"
      viewBox="0 0 50 30"
    >
          <path d="M39.066 10.805a.56.56 0 10-.001-*********** 0 00.001 1.121M40.557 9.727a.647.647 0 100-1.295.647.647 0 000 1.295M41.398 19.991h-3.579c-.4 0-.636-.45-.408-.779l1.83-2.649a.497.497 0 01.823.009l1.748 2.649a.496.496 0 01-.414.77m2.177-.681l-2.543-4.202v-2.346h.061a.667.667 0 000-1.333h-2.965a.666.666 0 100 1.333h.062v2.346l-2.544 4.202a1.347 1.347 0 001.206 1.946h5.518c1 0 1.651-1.051 1.205-1.946"></path>
        <path d="M46.73 4.942l.***************.***************.***************.***************.***************.***************.***************.***************.***************.***************.***************.*************.***************.***************.***************.**************.**************.***************.***************.************.***************.***************.***************.**************.**************.***************.**************.***************.***************.***************.***************.001.078v14.082l-.001.078-.003.078-.005.077-.007.076-.008.076-.011.075-.012.074-.014.074-.016.074-.018.072-.02.072-.021.071-.023.071-.024.07-.026.069-.028.068-.03.067-.031.066-.032.066-.034.064-.036.064-.037.063-.039.062-.04.06-.041.06-.043.059-.044.057-.045.057-.047.055-.048.054-.05.053-.05.052-.052.051-.053.049-.054.048-.056.047-.056.045-.058.044-.058.043-.06.042-.061.04-.061.038-.063.037-.064.036-.064.034-.066.032-.066.032-.068.029-.068.028-.069.026-.069.025-.071.022-.071.022-.072.019-.073.018-.073.016-.074.014-.074.012-.076.011-.075.009-.077.006-.077.005-.077.003-.078.001H32.569l-.078-.001-.077-.003-.077-.005-.076-.006-.076-.009-.075-.011-.075-.012-.074-.014-.073-.016-.073-.018-.071-.019-.072-.022-.07-.022-.07-.025-.069-.026-.068-.028-.067-.029-.067-.032-.065-.032-.065-.034-.063-.036-.063-.037-.062-.038-.06-.04-.06-.042-.059-.043-.057-.044-.057-.045-.055-.047-.054-.048-.053-.049-.052-.051-.051-.052-.049-.053-.048-.054-.047-.055-.045-.057-.045-.057-.042-.059-.042-.06-.04-.06-.038-.062-.037-.063-.036-.064-.034-.064-.033-.066-.031-.066-.029-.067-.028-.068-.026-.069-.025-.07-.023-.071-.021-.071-.019-.072-.018-.072-.016-.074-.014-.074-.013-.074-.01-.075-.009-.076-.007-.076-.005-.077-.002-.078-.001-.078V7.959l.001-.078.002-.078.005-.077.007-.076.009-.076.01-.075.013-.074.014-.074.016-.074.018-.072.019-.072.021-.071.023-.071.025-.07.026-.069.028-.068.029-.067.031-.066.033-.066.034-.064.036-.064.037-.063.038-.062.04-.06.042-.06.042-.059.045-.057.045-.057.047-.055.048-.054.049-.053.051-.052.052-.051.053-.049.054-.048.055-.047.057-.045.057-.044.059-.043.06-.042.06-.04.062-.038.063-.037.063-.036.065-.034.065-.032.067-.032.067-.029.068-.028.069-.026.07-.025.07-.022.072-.022.071-.019.073-.018.073-.016.074-.014.075-.012.075-.011.076-.009.076-.006.077-.005.077-.003.078-.001h14.083l.078.001zM32.579 6.441h-.05l-.039.002-.039.002-.038.004-.038.004-.038.005-.038.007-.037.007-.036.008-.036.009-.036.009-.036.011-.035.011-.035.013-.034.013-.034.014-.034.014-.033.016-.033.016-.032.017-.032.018-.032.019-.031.019-.031.02-.03.021-.029.022-.029.022-.029.023-.028.024-.027.024-.027.025-.026.026-.026.026-.025.027-.024.027-.024.028-.023.029-.022.029-.022.03-.021.03-.02.03-.019.031-.019.032-.018.032-.017.032-.016.033-.016.033-.015.034-.014.034-.013.034-.012.035-.011.035-.011.036-.01.036-.009.036-.008.037-.007.037-.006.037-.005.038-.004.038-.004.038-.002.039-.002.04V22.081l.002.04.002.039.004.038.004.038.005.038.006.037.007.037.008.037.009.036.01.036.011.036.011.035.012.035.013.034.014.034.015.034.016.033.016.033.017.032.018.032.019.032.019.031.02.03.021.03.022.03.022.029.023.029.024.028.024.027.025.027.026.026.026.026.027.025.027.024.028.024.029.023.029.022.029.022.03.021.031.02.031.019.032.019.032.018.032.017.033.016.033.016.034.014.034.014.034.013.035.013.035.011.036.011.036.009.036.009.036.008.037.007.038.007.038.005.038.004.038.004.039.002.039.002h14.163l.039-.002.039-.002.039-.004.038-.004.038-.005.037-.007.037-.007.037-.008.036-.009.036-.009.035-.011.036-.011.034-.013.035-.013.034-.014.033-.014.034-.016.032-.016.033-.017.032-.018.031-.019.031-.019.031-.02.03-.021.03-.022.029-.022.028-.023.028-.024.028-.024.027-.025.026-.026.026-.026.025-.027.024-.027.024-.028.023-.029.022-.029.022-.03.02-.03.021-.03.019-.031.019-.032.018-.032.017-.032.016-.033.016-.033.014-.034.014-.034.013-.034.013-.035.011-.035.011-.036.009-.036.009-.036.008-.037.007-.037.006-.037.006-.038.004-.038.003-.038.003-.039.001-.04.001-.049V7.968l-.001-.049-.001-.04-.003-.039-.003-.038-.004-.038-.006-.038-.006-.037-.007-.037-.008-.037-.009-.036-.009-.036-.011-.036-.011-.035-.013-.035-.013-.034-.014-.034-.014-.034-.016-.033-.016-.033-.017-.032-.018-.032-.019-.032-.019-.031-.021-.03-.02-.03-.022-.03-.022-.029-.023-.029-.024-.028-.024-.027-.025-.027-.026-.026-.026-.026-.027-.025-.028-.024-.028-.024-.028-.023-.029-.022-.03-.022-.03-.021-.031-.02-.031-.019-.031-.019-.032-.018-.033-.017-.032-.016-.034-.016-.033-.014-.034-.014-.035-.013-.034-.013-.036-.011-.035-.011-.036-.009-.036-.009-.037-.008-.037-.007-.037-.007-.038-.005-.038-.004-.039-.004-.039-.002-.039-.002H32.579z"></path>
        <path d="M6.37 8.104V9.47H4.224v5.749H2.622V9.47H.476V8.104H6.37z"></path>
        <path d="M7.396 8.104H8.998V15.219H7.396z"></path>
        <path d="M15.918 8.104V9.47h-2.146v5.749h-1.601V9.47h-2.146V8.104h5.893zM21.699 13.844v1.375h-4.754V8.104h1.602v5.74h3.152zM22.725 15.219V8.104h4.805v1.345h-3.203v1.397h2.844v1.314h-2.844v1.714h3.44v1.345h-5.042z"></path>
          <path d="M4.282 17.216v.881H2.897v3.713H1.863v-3.713H.477v-.881h3.805zM7.477 21.81a10.389 10.389 0 00-.162-.491c-.06-.168-.118-.336-.176-.504h-1.79c-.057.168-.116.336-.175.504-.06.168-.114.332-.163.491H3.937a85.2 85.2 0 01.491-1.373c.155-.419.306-.815.454-1.186.148-.371.294-.724.438-1.057.143-.334.292-.66.447-.978h.988a37.492 37.492 0 011.339 3.221c.155.42.318.878.49 1.373H7.477zm-1.239-3.554a5.468 5.468 0 01-.1.272l-.152.398a56.25 56.25 0 00-.398 1.067h1.306a26.56 26.56 0 00-.394-1.067c-.06-.15-.112-.283-.156-.398l-.106-.272zM11.256 18.011c-.482 0-.83.134-1.044.401-.214.268-.322.633-.322 1.097 0 .226.027.43.08.614.053.183.133.341.239.474.106.132.238.235.397.308.159.073.345.109.557.109.115 0 .213-.002.295-.006.082-.005.154-.014.216-.027v-1.598h1.034v2.274a3.832 3.832 0 01-.597.156 5.256 5.256 0 01-1.014.083c-.345 0-.658-.053-.938-.159a1.953 1.953 0 01-1.18-1.213 2.953 2.953 0 01-.163-1.015c0-.384.06-.724.179-1.021a2.21 2.21 0 01.491-.752c.208-.205.452-.361.733-.467.28-.106.58-.159.898-.159a3.594 3.594 0 011.037.142 2.132 2.132 0 01.487.209l-.298.829a2.614 2.614 0 00-.487-.196 2.077 2.077 0 00-.6-.083zM16.738 20.921v.889h-3.069v-4.594h1.034v3.705h2.035z"></path>
          <path d="M17.401 17.216H18.435V21.810000000000002H17.401z"></path>
          <path d="M22.579 21.81a23.197 23.197 0 00-.962-1.558 15.615 15.615 0 00-1.1-1.452v3.01h-1.021v-4.594h.842c.146.146.307.325.484.537.177.212.357.438.54.679.183.241.366.491.547.749.181.259.351.507.51.746v-2.711h1.028v4.594h-.868zM24.508 21.81v-4.594h3.102v.868h-2.068v.902h1.836v.848h-1.836v1.107h2.22v.869h-3.254z"></path>
    </svg>,
	topLogoTitleTag:  <svg
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 50 30"
  >
		<path d="M24.55 5.112a.463.463 0 100-.925.463.463 0 000 .925M25.78 4.223a.536.536 0 10-.001-1.071.536.536 0 00.001 1.071M26.474 12.691h-2.952a.41.41 0 01-.337-.642l1.51-2.186a.41.41 0 01.679.007l1.442 2.186a.41.41 0 01-.342.635m1.797-.562l-2.099-3.466V6.726h.051a.55.55 0 100-1.099h-2.446a.55.55 0 100 1.099h.051v1.937l-2.099 3.466a1.11 1.11 0 00.994 1.606h4.554a1.11 1.11 0 00.994-1.606"></path>
	  <path d="M30.874.274l.***************.***************.**************.***************.**************.***************.***************.***************.***************.***************.***************.**************.***************.***************.*************.***************.***************.***************.***************.*************.***************.***************.***************.***************.**************.***************.**************.***************.**************.***************.***************.001.064v11.62l-.001.064-.002.064-.004.064-.006.063-.007.062-.009.062-.01.062-.012.061-.013.06-.015.06-.016.06-.017.058-.019.058-.02.058-.022.057-.023.056-.024.056-.026.054-.027.054-.028.054-.029.052-.031.052-.032.051-.033.05-.034.049-.035.049-.036.047-.038.047-.039.045-.039.045-.041.044-.042.043-.043.041-.043.041-.045.04-.046.038-.046.038-.048.036-.048.036-.049.034-.05.033-.051.031-.052.031-.053.029-.053.029-.054.026-.055.026-.055.024-.056.023-.057.022-.058.02-.058.019-.059.018-.059.016-.06.014-.061.014-.061.011-.061.01-.062.009-.063.007-.063.006-.063.004-.064.002-.064.001H19.19l-.064-.001-.064-.002-.063-.004-.063-.006-.063-.007-.062-.009-.061-.01-.061-.011-.061-.014-.06-.014-.059-.016-.059-.018-.058-.019-.058-.02-.057-.022-.056-.023-.055-.024-.055-.026-.054-.026-.053-.029-.053-.029-.052-.031-.051-.031-.05-.033-.049-.034-.048-.036-.048-.036-.046-.038-.046-.038-.045-.04-.043-.041-.043-.041-.042-.043-.041-.044-.039-.045-.039-.045-.038-.047-.036-.047-.035-.049-.034-.049-.033-.05-.032-.051-.031-.052-.029-.052-.028-.054-.027-.054-.026-.054-.024-.056-.023-.056-.022-.057-.02-.058-.019-.058-.017-.058-.016-.06-.015-.06-.013-.06-.012-.061-.01-.062-.009-.062-.007-.062-.006-.063-.004-.064-.002-.064-.001-.064V2.763l.001-.064.002-.064.004-.063.006-.063.007-.063.009-.062.01-.061.012-.061.013-.061.015-.06.016-.059.017-.059.019-.058.02-.057.022-.057.023-.056.024-.056.026-.055.027-.054.028-.053.029-.053.031-.051.032-.051.033-.05.034-.05.035-.048.036-.048.038-.046.039-.046.039-.044.041-.044.042-.043.043-.042.043-.04.045-.04.046-.039.046-.037.048-.037.048-.035.049-.034.05-.033.051-.032.052-.031.053-.029.053-.028.054-.027.055-.026.055-.024.056-.023.057-.021.058-.021.058-.019.059-.017.059-.016.06-.015.061-.013.061-.012.061-.01.062-.009.063-.007.063-.005.063-.004.064-.003.064-.001h11.62l.064.001zM19.2 1.773l-.036.001-.026.001-.025.001-.026.003-.024.003-.025.003-.024.004-.024.005-.024.005-.024.006-.023.006-.023.007-.023.007-.023.008-.022.009-.022.009-.022.009-.022.01-.021.011-.021.011-.021.012-.02.012-.021.013-.02.013-.019.013-.02.015-.019.014-.018.015-.019.016-.018.016-.017.016-.018.017-.016.017-.017.018-.016.017-.015.019-.015.019-.015.019-.014.019-.013.02-.014.02-.012.02-.012.02-.012.021-.011.021-.011.022-.01.021-.01.022-.009.022-.008.023-.008.022-.008.023-.006.023-.007.023-.005.024-.006.024-.004.024-.004.024-.004.025-.003.025-.002.025-.001.025-.001.026-.001.036v11.601l.001.035.001.026.001.026.002.025.003.025.004.024.004.025.004.024.006.024.005.023.007.023.006.023.008.023.008.023.008.022.009.022.01.022.01.022.011.021.011.021.012.021.012.021.012.02.014.02.013.02.014.019.015.019.015.019.015.018.016.018.017.018.016.017.018.017.017.016.018.016.019.015.018.015.019.015.02.014.019.014.02.013.021.013.02.012.021.011.021.012.021.01.022.01.022.01.022.009.022.008.023.008.023.008.023.007.023.006.024.006.024.005.024.005.024.004.025.003.024.003.026.002.025.002.026.001H30.836l.026-.001.025-.002.026-.002.024-.003.025-.003.024-.004.024-.005.024-.005.024-.006.023-.006.023-.007.023-.008.023-.008.022-.008.022-.009.022-.01.022-.01.021-.01.021-.012.021-.011.02-.012.021-.013.02-.013.019-.014.02-.014.019-.015.018-.015.019-.015.018-.016.017-.016.018-.017.016-.017.017-.018.016-.018.015-.018.015-.019.015-.019.014-.019.013-.02.014-.02.012-.02.012-.021.012-.021.011-.021.011-.021.01-.022.01-.022.009-.022.008-.022.008-.023.008-.023.006-.023.007-.023.005-.023.006-.024.004-.024.004-.025.004-.024.003-.025.002-.025.001-.026.001-.026.001-.035V2.773l-.001-.036-.001-.026-.001-.025-.002-.025-.003-.025-.004-.025-.004-.024-.004-.024-.006-.024-.005-.023-.007-.024-.006-.023-.008-.023-.008-.022-.008-.023-.009-.022-.01-.022-.01-.021-.011-.022-.011-.021-.012-.021-.012-.02-.012-.02-.014-.02-.013-.02-.014-.019-.015-.019-.015-.019-.015-.019-.016-.017-.017-.018-.016-.017-.018-.017-.017-.016-.018-.016-.019-.016-.018-.015-.019-.014-.02-.015-.019-.013-.02-.013-.021-.013-.02-.012-.021-.012-.021-.011-.021-.011-.022-.01-.022-.009-.022-.009-.022-.009-.023-.008-.023-.007-.023-.007-.023-.006-.024-.006-.024-.005-.024-.005-.024-.004-.025-.003-.024-.003-.026-.003-.025-.001-.026-.001-.036-.001H19.2z"></path>
	  <path d="M18.613 18.217v1.125h-1.768v4.738h-1.32v-4.738h-1.768v-1.125h4.856z"></path>
	  <path d="M19.459 18.217H20.779V24.08H19.459z"></path>
	  <path d="M26.48 18.217v1.125h-1.768v4.738h-1.319v-4.738h-1.768v-1.125h4.855zM31.243 22.946v1.134h-3.917v-5.863h1.32v4.729h2.597zM32.089 24.08v-5.863h3.96v1.108h-2.64v1.151h2.343v1.083h-2.343v1.413h2.834v1.108h-4.154z"></path>
		<path d="M16.893 25.725v.726h-1.142v3.059h-.852v-3.059h-1.141v-.726h3.135zM19.526 29.51a9.526 9.526 0 00-.134-.404c-.049-.139-.098-.277-.145-.415h-1.475l-.144.415c-.05.138-.094.273-.134.404h-.885c.142-.408.277-.785.404-1.131a40.258 40.258 0 01.735-1.849c.118-.275.241-.543.368-.805h.814a28.892 28.892 0 01.729 1.677c.122.305.247.631.375.977.127.346.262.723.404 1.131h-.912zm-1.022-2.928a4.547 4.547 0 01-.082.224l-.125.328a36.184 36.184 0 00-.328.879h1.076c-.058-.164-.115-.32-.169-.469-.055-.15-.107-.286-.156-.41a26.237 26.237 0 01-.128-.328 28.526 28.526 0 00-.088-.224zM22.639 26.38c-.397 0-.684.11-.86.331-.177.22-.265.521-.265.904 0 .185.022.354.065.505.044.151.11.281.197.39.087.11.197.194.328.254s.284.09.459.09c.094 0 .175-.001.243-.005.067-.004.126-.011.177-.022v-1.316h.852v1.873c-.102.04-.266.083-.491.129a4.369 4.369 0 01-.836.068 2.18 2.18 0 01-.773-.131 1.623 1.623 0 01-.593-.383 1.71 1.71 0 01-.379-.617 2.426 2.426 0 01-.134-.835c0-.317.049-.598.147-.842.099-.244.234-.45.405-.62.171-.169.372-.297.603-.385.231-.087.478-.131.74-.131a2.957 2.957 0 01.855.118 1.804 1.804 0 01.402.172l-.246.683a2.274 2.274 0 00-.402-.162 1.725 1.725 0 00-.494-.068zM27.156 28.778v.732h-2.529v-3.785h.852v3.053h1.677z"></path>
		<path d="M27.702 25.725H28.554000000000002V29.51H27.702z"></path>
		<path d="M31.968 29.51a19.313 19.313 0 00-.792-1.284 13.158 13.158 0 00-.906-1.196v2.48h-.842v-3.785h.694c.12.12.253.267.399.442.146.175.294.361.445.56.151.198.301.404.451.617.149.213.289.418.42.615v-2.234h.847v3.785h-.716zM33.558 29.51v-3.785h2.556v.715H34.41v.743h1.513v.699H34.41v.912h1.83v.716h-2.682z"></path>
  </svg>,
  topLogoTitle: <svg
  xmlns="http://www.w3.org/2000/svg"
  fillRule="evenodd"
  strokeLinejoin="round"
  strokeMiterlimit="2"
  clipRule="evenodd"
  viewBox="0 0 50 30"
>
	  <path d="M24.455 6.271a.561.561 0 100-1.123.561.561 0 000 1.123M25.946 5.193a.648.648 0 10-.001-1.295.648.648 0 00.001 1.295M26.787 15.456h-3.579a.496.496 0 01-.408-.778l1.83-2.65a.497.497 0 01.823.009l1.748 2.649a.496.496 0 01-.414.77m2.178-.681l-2.544-4.201V8.227h.062a.666.666 0 100-1.333h-2.966a.667.667 0 000 1.333h.062v2.347l-2.543 4.201a1.347 1.347 0 001.205 1.947h5.518c1 0 1.651-1.052 1.206-1.947"></path>
	<path d="M32.119.407l.***************.***************.**************.***************.**************.***************.**************.***************.***************.***************.***************.************.***************.***************.***************.***************.***************.***************.***************.**************.***************.***************.***************.***************.**************.**************.***************.***************.***************.***************.***************.001.078v14.083l-.001.078-.003.077-.005.077-.006.076-.009.076-.011.075-.012.075-.014.074-.016.073-.018.073-.019.072-.022.071-.022.07-.025.07-.026.069-.028.068-.029.067-.032.067-.032.065-.034.065-.036.064-.037.062-.038.062-.04.061-.042.059-.043.059-.044.058-.045.056-.047.055-.048.055-.049.053-.051.051-.052.051-.053.049-.054.049-.055.046-.057.046-.057.044-.059.043-.06.041-.06.04-.062.039-.063.037-.064.035-.064.034-.066.033-.066.031-.067.029-.068.028-.069.027-.07.024-.071.023-.071.021-.072.02-.072.017-.074.016-.074.015-.074.012-.075.011-.076.008-.076.007-.077.005-.078.003-.078.001H17.959l-.078-.001-.078-.003-.077-.005-.076-.007-.076-.008-.075-.011-.074-.012-.074-.015-.074-.016-.072-.017-.072-.02-.071-.021-.071-.023-.07-.024-.069-.027-.068-.028-.067-.029-.066-.031-.066-.033-.064-.034-.064-.035-.063-.037-.062-.039-.06-.04-.06-.041-.059-.043-.057-.044-.057-.046-.055-.046-.054-.049-.053-.049-.052-.051-.051-.051-.049-.053-.048-.055-.047-.055-.045-.056-.044-.058-.043-.059-.042-.059-.04-.061-.038-.062-.037-.062-.036-.064-.034-.065-.032-.065-.032-.067-.029-.067-.028-.068-.026-.069-.025-.07-.022-.07-.022-.071-.019-.072-.018-.073-.016-.073-.014-.074-.012-.075-.011-.075-.009-.076-.006-.076-.005-.077-.003-.077-.001-.078V3.424l.001-.078.003-.077.005-.077.006-.076.009-.076.011-.075.012-.075.014-.074.016-.073.018-.073.019-.072.022-.071.022-.07.025-.07.026-.069.028-.068.029-.067.032-.067.032-.065.034-.065.036-.064.037-.062.038-.062.04-.061.042-.059.043-.059.044-.058.045-.056.047-.055.048-.055.049-.053.051-.051.052-.051.053-.049.054-.048.055-.047.057-.046.057-.044.059-.043.06-.041.06-.04.062-.039.063-.037.064-.035.064-.034.066-.033.066-.031.067-.029.068-.028.069-.027.07-.024.071-.023.071-.021.072-.02.072-.017.074-.016.074-.015.074-.012.075-.01.076-.009.076-.007.077-.005.078-.003.078-.001h14.082l.078.001zM17.968 1.906l-.049.001-.04.001-.039.003-.038.003-.038.005-.038.005-.037.006-.037.007-.037.008-.036.009-.036.01-.036.01-.035.012-.035.012-.034.013-.034.014-.034.015-.033.015-.033.017-.032.017-.032.018-.032.018-.031.02-.03.02-.03.021-.03.021-.029.023-.029.023-.028.023-.027.025-.027.025-.026.025-.026.027-.025.026-.024.028-.024.028-.023.028-.022.029-.022.03-.021.03-.02.031-.019.031-.019.031-.018.032-.017.033-.016.033-.016.033-.014.034-.014.034-.013.034-.013.035-.011.035-.011.035-.009.036-.009.037-.008.036-.007.037-.007.038-.005.037-.004.038-.004.039-.002.039-.002.039v14.163l.002.039.002.039.004.039.004.038.005.037.007.038.007.037.008.036.009.037.009.036.011.035.011.035.013.035.013.034.014.034.014.034.016.033.016.033.017.033.018.032.019.031.019.031.02.031.021.03.022.03.022.029.023.028.024.028.024.028.025.027.026.026.026.025.027.025.027.025.028.023.029.023.029.023.03.021.03.021.03.02.031.02.032.018.032.018.032.017.033.017.033.015.034.015.034.014.034.013.035.012.035.012.036.01.036.01.036.009.037.008.037.007.037.006.038.005.038.005.038.003.039.003.04.001.049.001h14.064l.049-.001.04-.001.039-.003.038-.003.038-.005.038-.005.037-.006.037-.007.037-.008.036-.009.036-.01.036-.01.035-.012.035-.012.034-.013.034-.014.034-.015.033-.015.033-.017.032-.017.032-.018.032-.018.031-.02.03-.02.03-.021.03-.021.029-.023.029-.023.028-.023.027-.025.027-.025.026-.025.026-.027.025-.026.024-.028.024-.028.023-.028.022-.029.022-.03.021-.03.02-.031.019-.031.019-.031.018-.032.017-.033.016-.033.016-.033.014-.034.014-.034.013-.034.013-.035.011-.035.011-.035.009-.036.009-.037.008-.036.007-.037.007-.038.005-.037.004-.038.004-.039.002-.039.002-.039V3.384l-.002-.039-.002-.039-.004-.039-.004-.038-.005-.037-.007-.038-.007-.037-.008-.036-.009-.037-.009-.036-.011-.035-.011-.035-.013-.035-.013-.034-.014-.034-.014-.034-.016-.033-.016-.033-.017-.033-.018-.032-.019-.031-.019-.031-.02-.031-.021-.03-.022-.029-.022-.03-.023-.028-.024-.028-.024-.028-.025-.026-.026-.027-.026-.025-.027-.025-.027-.025-.028-.023-.029-.023-.029-.023-.03-.021-.03-.021-.03-.02-.031-.02-.032-.018-.032-.018-.032-.017-.033-.017-.033-.015-.034-.015-.034-.014-.034-.013-.035-.012-.035-.012-.036-.01-.036-.01-.036-.009-.037-.008-.037-.007-.037-.006-.038-.005-.038-.005-.038-.003-.039-.003-.04-.001-.049-.001H17.968z"></path>
	<path d="M17.248 22.408v1.366h-2.146v5.75h-1.601v-5.75h-2.146v-1.366h5.893z"></path>
	<path d="M18.275 22.408H19.877V29.523000000000003H18.275z"></path>
	<path d="M26.797 22.408v1.366h-2.146v5.75h-1.602v-5.75h-2.146v-1.366h5.894zM32.577 28.148v1.376h-4.753v-7.116h1.601v5.74h3.152zM33.604 29.524v-7.116h4.805v1.345h-3.203v1.397h2.844v1.314h-2.844v1.715h3.439v1.345h-5.041z"></path>
</svg>,
  topTitleLogo: <svg
  xmlns="http://www.w3.org/2000/svg"
  fillRule="evenodd"
  strokeLinejoin="round"
  strokeMiterlimit="2"
  clipRule="evenodd"
  viewBox="0 0 50 30"
>
	  <path d="M24.455 15.239a.561.561 0 100-1.123.561.561 0 000 1.123M25.946 14.161a.648.648 0 10-.001-1.295.648.648 0 00.001 1.295M26.787 24.424h-3.579a.496.496 0 01-.408-.778l1.83-2.65a.497.497 0 01.823.009l1.748 2.649a.496.496 0 01-.414.77m2.178-.681l-2.544-4.201v-2.347h.062a.666.666 0 100-1.333h-2.966a.667.667 0 000 1.333h.062v2.347l-2.543 4.201a1.347 1.347 0 001.205 1.947h5.518c1 0 1.651-1.052 1.206-1.947"></path>
	<path d="M32.119 9.375l.***************.***************.**************.***************.**************.***************.**************.***************.***************.***************.***************.************.***************.***************.***************.***************.***************.***************.***************.**************.***************.***************.***************.***************.**************.**************.***************.***************.***************.***************.***************.001.078v14.083l-.001.078-.003.077-.005.077-.006.076-.009.076-.011.075-.012.075-.014.074-.016.073-.018.073-.019.072-.022.071-.022.07-.025.07-.026.069-.028.068-.029.067-.032.067-.032.065-.034.065-.036.064-.037.062-.038.062-.04.061-.042.059-.043.059-.044.058-.045.056-.047.055-.048.055-.049.053-.051.051-.052.051-.053.049-.054.049-.055.046-.057.046-.057.044-.059.043-.06.041-.06.04-.062.039-.063.037-.064.035-.064.034-.066.033-.066.031-.067.029-.068.028-.069.027-.07.024-.071.023-.071.021-.072.02-.072.017-.074.016-.074.015-.074.012-.075.011-.076.008-.076.007-.077.005-.078.003-.078.001H17.959l-.078-.001-.078-.003-.077-.005-.076-.007-.076-.008-.075-.011-.074-.012-.074-.015-.074-.016-.072-.017-.072-.02-.071-.021-.071-.023-.07-.024-.069-.027-.068-.028-.067-.029-.066-.031-.066-.033-.064-.034-.064-.035-.063-.037-.062-.039-.06-.04-.06-.041-.059-.043-.057-.044-.057-.046-.055-.046-.054-.049-.053-.049-.052-.051-.051-.051-.049-.053-.048-.055-.047-.055-.045-.056-.044-.058-.043-.059-.042-.059-.04-.061-.038-.062-.037-.062-.036-.064-.034-.065-.032-.065-.032-.067-.029-.067-.028-.068-.026-.069-.025-.07-.022-.07-.022-.071-.019-.072-.018-.073-.016-.073-.014-.074-.012-.075-.011-.075-.009-.076-.006-.076-.005-.077-.003-.077-.001-.078V12.392l.001-.078.003-.077.005-.077.006-.076.009-.076.011-.075.012-.075.014-.074.016-.073.018-.073.019-.072.022-.071.022-.07.025-.07.026-.069.028-.068.029-.067.032-.067.032-.065.034-.065.036-.064.037-.062.038-.062.04-.061.042-.059.043-.059.044-.058.045-.056.047-.055.048-.055.049-.053.051-.051.052-.051.053-.049.054-.048.055-.047.057-.046.057-.044.059-.043.06-.041.06-.04.062-.039.063-.037.064-.035.064-.034.066-.033.066-.031.067-.029.068-.028.069-.027.07-.024.071-.023.071-.021.072-.02.072-.017.074-.016.074-.015.074-.012.075-.01.076-.009.076-.007.077-.005.078-.003.078-.001h14.082l.078.001zm-14.151 1.499l-.049.001-.04.001-.039.003-.038.003-.038.005-.038.005-.037.006-.037.007-.037.008-.036.009-.036.01-.036.01-.035.012-.035.012-.034.013-.034.014-.034.015-.033.015-.033.017-.032.017-.032.018-.032.018-.031.02-.03.02-.03.021-.03.021-.029.023-.029.023-.028.023-.027.025-.027.025-.026.025-.026.027-.025.026-.024.028-.024.028-.023.028-.022.029-.022.03-.021.03-.02.031-.019.031-.019.031-.018.032-.017.033-.016.033-.016.033-.014.034-.014.034-.013.034-.013.035-.011.035-.011.035-.009.036-.009.037-.008.036-.007.037-.007.038-.005.037-.004.038-.004.039-.002.039-.002.039V26.515l.002.039.002.039.004.039.004.038.005.037.007.038.007.037.008.036.009.037.009.036.011.035.011.035.013.035.013.034.014.034.014.034.016.033.016.033.017.033.018.032.019.031.019.031.02.031.021.03.022.03.022.029.023.028.024.028.024.028.025.026.026.027.026.025.027.025.027.025.028.023.029.023.029.023.03.021.03.021.03.02.031.02.032.018.032.018.032.017.033.017.033.015.034.015.034.014.034.013.035.012.035.012.036.01.036.01.036.009.037.008.037.007.037.006.038.005.038.005.038.003.039.003.04.001.049.001h14.064l.049-.001.04-.001.039-.003.038-.003.038-.005.038-.005.037-.006.037-.007.037-.008.036-.009.036-.01.036-.01.035-.012.035-.012.034-.013.034-.014.034-.015.033-.015.033-.017.032-.017.032-.018.032-.018.031-.02.03-.02.03-.021.03-.021.029-.023.029-.023.028-.023.027-.025.027-.025.026-.025.026-.027.025-.026.024-.028.024-.028.023-.028.022-.029.022-.03.021-.03.02-.031.019-.031.019-.031.018-.032.017-.033.016-.033.016-.033.014-.034.014-.034.013-.034.013-.035.011-.035.011-.035.009-.036.009-.037.008-.036.007-.037.007-.038.005-.037.004-.038.004-.039.002-.039.002-.039V12.352l-.002-.039-.002-.039-.004-.039-.004-.038-.005-.037-.007-.038-.007-.037-.008-.036-.009-.037-.009-.036-.011-.035-.011-.035-.013-.035-.013-.034-.014-.034-.014-.034-.016-.033-.016-.033-.017-.033-.018-.032-.019-.031-.019-.031-.02-.031-.021-.03-.022-.03-.022-.029-.023-.028-.024-.028-.024-.028-.025-.026-.026-.027-.026-.025-.027-.025-.027-.025-.028-.023-.029-.023-.029-.023-.03-.021-.03-.021-.03-.02-.031-.02-.032-.018-.032-.018-.032-.017-.033-.017-.033-.015-.034-.015-.034-.014-.034-.013-.035-.012-.035-.012-.036-.01-.036-.01-.036-.009-.037-.008-.037-.007-.037-.006-.038-.005-.038-.005-.038-.003-.039-.003-.04-.001-.049-.001H17.968z"></path>
	<path d="M17.248.447v1.365h-2.146v5.75h-1.601v-5.75h-2.146V.447h5.893z"></path>
	<path d="M18.275 0.447H19.877V7.562H18.275z"></path>
	<path d="M26.797.447v1.365h-2.146v5.75h-1.602v-5.75h-2.146V.447h5.894zM32.577 6.186v1.376h-4.753V.447h1.601v5.739h3.152zM33.604 7.562V.447h4.805v1.345h-3.203v1.396h2.844v1.314h-2.844v1.715h3.439v1.345h-5.041z"></path>
</svg>,
  topTitleTagLogo: <svg
  xmlns="http://www.w3.org/2000/svg"
  fillRule="evenodd"
  strokeLinejoin="round"
  strokeMiterlimit="2"
  clipRule="evenodd"
  viewBox="0 0 50 30"
>
	  <path d="M24.55 18.025a.463.463 0 100-.925.463.463 0 000 .925M25.78 17.135a.535.535 0 100-1.07.535.535 0 000 1.07M26.474 25.604h-2.952a.41.41 0 01-.337-.643l1.51-2.186a.41.41 0 01.679.008l1.442 2.185a.41.41 0 01-.342.636m1.797-.562l-2.099-3.467v-1.936h.051a.55.55 0 100-1.1h-2.446a.55.55 0 100 1.1h.051v1.936l-2.099 3.467a1.11 1.11 0 00.994 1.606h4.554a1.11 1.11 0 00.994-1.606"></path>
	<path d="M30.874 13.187l.***************.***************.**************.***************.**************.***************.**************.***************.***************.***************.***************.**************.***************.***************.***************.***************.***************.***************.***************.**************.***************.***************.***************.***************.**************.***************.**************.**************.**************.***************.***************.001.064v11.62l-.001.064-.002.064-.004.063-.006.063-.007.063-.009.062-.01.061-.012.061-.013.061-.015.06-.016.059-.017.059-.019.058-.02.058-.022.056-.023.057-.024.055-.026.055-.027.054-.028.053-.029.053-.031.052-.032.05-.033.051-.034.049-.035.048-.036.048-.038.046-.039.046-.039.045-.041.043-.042.043-.043.042-.043.041-.045.039-.046.039-.046.037-.048.037-.048.035-.049.034-.05.033-.051.032-.052.031-.053.029-.053.028-.054.027-.055.026-.055.024-.056.023-.057.022-.058.02-.058.019-.059.017-.059.016-.06.015-.061.013-.061.012-.061.01-.062.009-.063.007-.063.006-.063.004-.064.002-.064.001H19.19l-.064-.001-.064-.002-.063-.004-.063-.006-.063-.007-.062-.009-.061-.01-.061-.012-.061-.013-.06-.015-.059-.016-.059-.017-.058-.019-.058-.02-.057-.022-.056-.023-.055-.024-.055-.026-.054-.027-.053-.028-.053-.029-.052-.031-.051-.032-.05-.033-.049-.034-.048-.035-.048-.037-.046-.037-.046-.039-.045-.039-.043-.041-.043-.042-.042-.043-.041-.043-.039-.045-.039-.046-.038-.046-.036-.048-.035-.048-.034-.049-.033-.051-.032-.05-.031-.052-.029-.053-.028-.053-.027-.054-.026-.055-.024-.055-.023-.057-.022-.056-.02-.058-.019-.058-.017-.059-.016-.059-.015-.06-.013-.061-.012-.061-.01-.061-.009-.062-.007-.063-.006-.063-.004-.063-.002-.064-.001-.064v-11.62l.001-.064.002-.064.004-.063.006-.063.007-.063.009-.062.01-.062.012-.061.013-.06.015-.06.016-.059.017-.059.019-.058.02-.058.022-.057.023-.056.024-.055.026-.055.027-.054.028-.053.029-.053.031-.052.032-.051.033-.05.034-.049.035-.048.036-.048.038-.046.039-.046.039-.045.041-.044.042-.042.043-.042.043-.041.045-.039.046-.039.046-.038.048-.036.048-.035.049-.034.05-.033.051-.032.052-.031.053-.029.053-.028.054-.027.055-.026.055-.024.056-.023.057-.022.058-.02.058-.019.059-.017.059-.017.06-.014.061-.013.061-.012.061-.01.062-.009.063-.007.063-.006.063-.004.064-.002.064-.001h11.62l.064.001zM19.2 14.686h-.036l-.026.001-.025.002-.026.002-.024.003-.025.004-.024.004-.024.004-.024.005-.024.006-.023.007-.023.006-.023.008-.023.008-.022.008-.022.009-.022.01-.022.01-.021.011-.021.011-.021.011-.02.013-.021.012-.02.013-.019.014-.02.014-.019.015-.018.015-.019.015-.018.016-.017.017-.018.016-.016.018-.017.017-.016.018-.015.018-.015.019-.015.019-.014.02-.014.019-.013.02-.012.02-.012.021-.012.021-.011.021-.011.021-.01.022-.01.022-.009.022-.008.022-.008.023-.008.023-.006.023-.007.023-.005.024-.006.023-.004.025-.004.024-.004.024-.003.025-.002.025-.001.026-.001.026-.001.035v11.601l.001.036.001.026.001.025.002.026.003.024.004.025.004.024.004.024.006.024.005.024.007.023.006.023.008.023.008.022.008.023.009.022.01.022.01.021.011.022.011.021.012.021.012.02.012.021.013.02.014.019.014.02.015.019.015.018.015.019.016.018.017.017.016.017.018.017.017.017.018.016.019.015.018.015.019.015.02.014.019.013.02.014.021.012.02.012.021.012.021.011.021.011.022.01.022.01.022.009.022.008.023.008.023.007.023.007.023.007.024.005.024.006.024.004.024.004.025.004.024.003.026.002.025.001.026.001.036.001h11.6l.036-.001.026-.001.025-.001.026-.002.024-.003.025-.004.024-.004.024-.004.024-.006.024-.005.023-.007.023-.007.023-.007.023-.008.022-.008.022-.009.022-.01.022-.01.021-.011.021-.011.021-.012.02-.012.021-.012.02-.014.019-.013.02-.014.019-.015.018-.015.019-.015.018-.016.017-.017.018-.017.016-.017.017-.017.016-.018.015-.019.015-.018.015-.019.014-.02.014-.019.013-.02.012-.021.012-.02.012-.021.011-.021.011-.022.01-.021.01-.022.009-.022.008-.023.008-.022.008-.023.006-.023.007-.023.005-.024.006-.024.004-.024.004-.024.004-.025.003-.024.002-.026.001-.025.001-.026.001-.036V15.685l-.001-.035-.001-.026-.001-.026-.002-.025-.003-.025-.004-.024-.004-.024-.004-.025-.006-.023-.005-.024-.007-.023-.006-.023-.008-.023-.008-.023-.008-.022-.009-.022-.01-.022-.01-.022-.011-.021-.011-.021-.012-.021-.012-.021-.012-.02-.013-.02-.014-.019-.014-.02-.015-.019-.015-.019-.015-.018-.016-.018-.017-.017-.016-.018-.018-.016-.017-.017-.018-.016-.019-.015-.018-.015-.019-.015-.02-.014-.019-.014-.02-.013-.021-.012-.02-.013-.021-.011-.021-.011-.021-.011-.022-.01-.022-.01-.022-.009-.022-.008-.023-.008-.023-.008-.023-.006-.023-.007-.024-.006-.024-.005-.024-.004-.024-.004-.025-.004-.024-.003-.026-.002-.025-.002-.026-.001H19.2z"></path>
	<path d="M18.613.297v1.125h-1.768v4.737h-1.32V1.422h-1.768V.297h4.856z"></path>
	<path d="M19.459 0.297H20.779V6.16H19.459z"></path>
	<path d="M26.48.297v1.125h-1.768v4.737h-1.319V1.422h-1.768V.297h4.855zM31.243 5.026v1.133h-3.917V.297h1.32v4.729h2.597zM32.089 6.159V.297h3.96v1.108h-2.64v1.15h2.343v1.083h-2.343v1.413h2.834v1.108h-4.154z"></path>
	  <path d="M16.893 7.804v.726h-1.142v3.059h-.852V8.53h-1.141v-.726h3.135zM19.526 11.589a9.526 9.526 0 00-.134-.404 34.118 34.118 0 01-.145-.415h-1.475c-.047.138-.095.277-.144.415-.05.138-.094.273-.134.404h-.885c.142-.408.277-.784.404-1.13.128-.346.252-.672.374-.978.122-.306.242-.596.361-.871.118-.275.241-.544.368-.806h.814a28.966 28.966 0 01.729 1.677c.122.306.247.632.375.978.127.346.262.722.404 1.13h-.912zm-1.022-2.927a4.96 4.96 0 01-.082.224l-.125.327a45.295 45.295 0 00-.328.88h1.076l-.169-.47a19.504 19.504 0 00-.156-.41 34.949 34.949 0 01-.128-.327l-.088-.224zM22.639 8.459c-.397 0-.684.111-.86.331-.177.22-.265.522-.265.904 0 .186.022.354.065.505.044.151.11.281.197.391a.886.886 0 00.328.254c.131.06.284.09.459.09.094 0 .175-.002.243-.006.067-.003.126-.011.177-.021V9.59h.852v1.874a3.3 3.3 0 01-.491.128 4.29 4.29 0 01-.836.068c-.284 0-.542-.043-.773-.131a1.621 1.621 0 01-.593-.382 1.718 1.718 0 01-.379-.617 2.432 2.432 0 01-.134-.836c0-.317.049-.597.147-.841.099-.244.234-.451.405-.62.171-.17.372-.298.603-.385.231-.088.478-.131.74-.131a2.963 2.963 0 01.855.117 1.681 1.681 0 01.402.172l-.246.683a2.2 2.2 0 00-.402-.161 1.69 1.69 0 00-.494-.069zM27.156 10.857v.732h-2.529V7.804h.852v3.053h1.677z"></path>
	  <path d="M27.702 7.804H28.554000000000002V11.589H27.702z"></path>
	  <path d="M31.968 11.589a19.04 19.04 0 00-.792-1.283 13.173 13.173 0 00-.906-1.197v2.48h-.842V7.804h.694c.12.12.253.268.399.442.146.175.294.362.445.56.151.199.301.405.451.618.149.213.289.417.42.614V7.804h.847v3.785h-.716zM33.558 11.589V7.804h2.556v.716H34.41v.742h1.513v.7H34.41v.912h1.83v.715h-2.682z"></path>
</svg>,
  topTitleLogoTag: <svg
  xmlns="http://www.w3.org/2000/svg"
  fillRule="evenodd"
  strokeLinejoin="round"
  strokeMiterlimit="2"
  clipRule="evenodd"
  viewBox="0 0 50 30"
>
	  <path d="M24.55 12.52a.463.463 0 100-.925.463.463 0 000 .925M25.78 11.63a.535.535 0 100-1.07.535.535 0 000 1.07M26.474 20.099h-2.952a.41.41 0 01-.337-.643l1.51-2.185a.41.41 0 01.679.007l1.442 2.186a.41.41 0 01-.342.635m1.797-.562l-2.099-3.467v-1.936h.051a.55.55 0 100-1.1h-2.446a.55.55 0 000 1.1h.051v1.936l-2.099 3.467a1.11 1.11 0 00.994 1.606h4.554a1.11 1.11 0 00.994-1.606"></path>
	<path d="M30.874 7.682l.***************.***************.**************.***************.**************.***************.**************.***************.***************.***************.***************.**************.***************.***************.***************.***************.***************.***************.***************.**************1.**************.***************.***************.***************.**************.***************.**************.***************.**************.***************.***************.001.064v11.62l-.001.064-.002.064-.004.063-.006.063-.007.063-.009.062-.01.062-.012.061-.013.06-.015.06-.016.059-.017.059-.019.058-.02.058-.022.057-.023.056-.024.055-.026.055-.027.054-.028.053-.029.053-.031.052-.032.051-.033.05-.034.049-.035.048-.036.048-.038.046-.039.046-.039.045-.041.044-.042.042-.043.042-.043.041-.045.039-.046.039-.046.038-.048.036-.048.035-.049.034-.05.033-.051.032-.052.031-.053.029-.053.028-.054.027-.055.026-.055.024-.056.023-.057.022-.058.02-.058.019-.059.017-.059.017-.06.014-.061.013-.061.012-.061.01-.062.009-.063.007-.063.006-.063.004-.064.002-.064.001H19.19l-.064-.001-.064-.002-.063-.004-.063-.006-.063-.007-.062-.009-.061-.01-.061-.012-.061-.013-.06-.014-.059-.017-.059-.017-.058-.019-.058-.02-.057-.022-.056-.023-.055-.024-.055-.026-.054-.027-.053-.028-.053-.029-.052-.031-.051-.032-.05-.033-.049-.034-.048-.035-.048-.036-.046-.038-.046-.039-.045-.039-.043-.041-.043-.042-.042-.042-.041-.044-.039-.045-.039-.046-.038-.046-.036-.048-.035-.048-.034-.049-.033-.05-.032-.051-.031-.052-.029-.053-.028-.053-.027-.054-.026-.055-.024-.055-.023-.056-.022-.057-.02-.058-.019-.058-.017-.059-.016-.059-.015-.06-.013-.06-.012-.061-.01-.062-.009-.062-.007-.063-.006-.063-.004-.063-.002-.064-.001-.064v-11.62l.001-.064.002-.064.004-.063.006-.063.007-.063.009-.062.01-.061.012-.061.013-.061.015-.06.016-.059.017-.059.019-.058.02-.058.022-.056.023-.057.024-.055.026-.055.027-.054.028-.053.029-.053.031-.052.032-.05.033-.051.034-.049.035-.048.036-.048.038-.046.039-.046.039-.045.041-.043.042-.043.043-.042.043-.041.045-.039.046-.039.046-.037.048-.037.048-.035.049-.034.05-.033.051-.032.052-.031.053-.029.053-.028.054-.027.055-.026.055-.024.056-.023.057-.022.058-.02.058-.019.059-.017.059-.016.06-.015.061-.013.061-.012.061-.01.062-.009.063-.007.063-.006.063-.004.064-.002.064-.001h11.62l.064.001zM19.2 9.181l-.036.001-.026.001-.025.001-.026.003-.024.002-.025.004-.024.004-.024.004-.024.006-.024.005-.023.007-.023.007-.023.007-.023.008-.022.008-.022.009-.022.01-.022.01-.021.011-.021.011-.021.012-.02.012-.021.012-.02.014-.019.013-.02.014-.019.015-.018.015-.019.016-.018.015-.017.017-.018.017-.016.017-.017.017-.016.018-.015.019-.015.018-.015.019-.014.02-.013.019-.014.02-.012.021-.012.02-.012.021-.011.021-.011.022-.01.021-.01.022-.009.022-.008.023-.008.022-.008.023-.006.023-.007.023-.005.024-.006.024-.004.024-.004.024-.004.025-.003.024-.002.026-.001.025-.001.026-.001.036v11.601l.001.035.001.026.001.026.002.025.003.025.004.024.004.024.004.025.006.023.005.024.007.023.006.023.008.023.008.023.008.022.009.022.01.022.01.022.011.021.011.021.012.021.012.021.012.02.014.02.013.019.014.02.015.019.015.019.015.018.016.018.017.017.016.018.018.016.017.017.018.016.019.015.018.015.019.015.02.014.019.014.02.013.021.012.02.013.021.011.021.011.021.011.022.01.022.01.022.009.022.008.023.008.023.008.023.006.023.007.024.006.024.005.024.004.024.004.025.004.024.003.026.002.025.002.026.001H30.836l.026-.001.025-.002.026-.002.024-.003.025-.004.024-.004.024-.004.024-.005.024-.006.023-.007.023-.006.023-.008.023-.008.022-.008.022-.009.022-.01.022-.01.021-.011.021-.011.021-.011.02-.013.021-.012.02-.013.019-.014.02-.014.019-.015.018-.015.019-.015.018-.016.017-.017.018-.016.016-.018.017-.017.016-.018.015-.018.015-.019.015-.019.014-.02.013-.019.014-.02.012-.02.012-.021.012-.021.011-.021.011-.021.01-.022.01-.022.009-.022.008-.022.008-.023.008-.023.006-.023.007-.023.005-.024.006-.023.004-.025.004-.024.004-.024.003-.025.002-.025.001-.026.001-.026.001-.035V10.181l-.001-.036-.001-.026-.001-.025-.002-.026-.003-.024-.004-.025-.004-.024-.004-.024-.006-.024-.005-.024-.007-.023-.006-.023-.008-.023-.008-.022-.008-.023-.009-.022-.01-.022-.01-.021-.011-.022-.011-.021-.012-.021-.012-.02-.012-.021-.014-.02-.013-.019-.014-.02-.015-.019-.015-.018-.015-.019-.016-.018-.017-.017-.016-.017-.018-.017-.017-.017-.018-.015-.019-.016-.018-.015-.019-.015-.02-.014-.019-.013-.02-.014-.021-.012-.02-.012-.021-.012-.021-.011-.021-.011-.022-.01-.022-.01-.022-.009-.022-.008-.023-.008-.023-.007-.023-.007-.023-.007-.024-.005-.024-.006-.024-.004-.024-.004-.025-.004-.024-.002-.026-.003-.025-.001-.026-.001-.036-.001H19.2z"></path>
	<path d="M18.613.297v1.125h-1.768v4.737h-1.32V1.422h-1.768V.297h4.856z"></path>
	<path d="M19.459 0.297H20.779V6.16H19.459z"></path>
	<path d="M26.48.297v1.125h-1.768v4.737h-1.319V1.422h-1.768V.297h4.855zM31.243 5.026v1.133h-3.917V.297h1.32v4.729h2.597zM32.089 6.159V.297h3.96v1.108h-2.64v1.15h2.343v1.083h-2.343v1.413h2.834v1.108h-4.154z"></path>
	  <path d="M16.893 25.935v.726h-1.142v3.059h-.852v-3.059h-1.141v-.726h3.135zM19.526 29.72a9.526 9.526 0 00-.134-.404c-.049-.139-.098-.277-.145-.416h-1.475a34.18 34.18 0 01-.144.416c-.05.138-.094.273-.134.404h-.885c.142-.408.277-.785.404-1.131a33.985 33.985 0 01.735-1.849c.118-.275.241-.543.368-.805h.814a28.892 28.892 0 01.729 1.676c.122.306.247.632.375.978.127.346.262.723.404 1.131h-.912zm-1.022-2.928a4.547 4.547 0 01-.082.224l-.125.328a44.432 44.432 0 00-.328.879h1.076l-.169-.47a19.3 19.3 0 00-.156-.409 26.237 26.237 0 01-.128-.328 28.526 28.526 0 00-.088-.224zM22.639 26.59c-.397 0-.684.11-.86.33-.177.221-.265.522-.265.904 0 .186.022.355.065.506.044.151.11.281.197.39a.886.886 0 00.328.254c.131.06.284.09.459.09.094 0 .175-.001.243-.005.067-.004.126-.011.177-.022v-1.316h.852v1.873c-.102.04-.266.083-.491.129a4.369 4.369 0 01-.836.068 2.18 2.18 0 01-.773-.131 1.623 1.623 0 01-.593-.383 1.71 1.71 0 01-.379-.617 2.428 2.428 0 01-.134-.836c0-.316.049-.597.147-.841.099-.244.234-.45.405-.62.171-.169.372-.297.603-.385.231-.087.478-.131.74-.131a2.957 2.957 0 01.855.118 1.69 1.69 0 01.402.172l-.246.682a2.353 2.353 0 00-.402-.161 1.725 1.725 0 00-.494-.068zM27.156 28.988v.732h-2.529v-3.785h.852v3.053h1.677z"></path>
	  <path d="M27.702 25.935H28.554000000000002V29.72H27.702z"></path>
	  <path d="M31.968 29.72a19.059 19.059 0 00-.792-1.284 13.158 13.158 0 00-.906-1.196v2.48h-.842v-3.785h.694c.12.12.253.267.399.442.146.175.294.361.445.56.151.198.301.404.451.617.149.213.289.418.42.615v-2.234h.847v3.785h-.716zM33.558 29.72v-3.785h2.556v.715H34.41v.743h1.513v.699H34.41v.912h1.83v.716h-2.682z"></path>
</svg>,
	px: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<g fillRule="nonzero">
	  <path d="M2.896 6.603h1.419v.92h.027c.21-.394.504-.677.88-.848a2.926 2.926 0 011.223-.256c.534 0 1.001.094 1.399.283.399.188.73.447.993.775.263.329.46.712.591 1.15.132.438.197.907.197 1.407 0 .455-.059.898-.177 1.327a3.455 3.455 0 01-.539 1.137 2.699 2.699 0 01-.913.789c-.368.197-.802.295-1.302.295-.219 0-.438-.019-.657-.059a2.855 2.855 0 01-.631-.19 2.51 2.51 0 01-.558-.336 1.823 1.823 0 01-.427-.479h-.027v3.391H2.896V6.603zm5.231 3.404c0-.306-.039-.604-.118-.894a2.418 2.418 0 00-.355-.768 1.829 1.829 0 00-.592-.539 1.603 1.603 0 00-.814-.204c-.631 0-1.107.219-1.427.657-.319.438-.479 1.021-.479 1.748 0 .342.041.66.125.953.083.294.208.546.374.756.167.21.366.377.598.499.232.123.502.184.809.184.341 0 .63-.07.867-.21.237-.14.432-.322.585-.545a2.21 2.21 0 00.328-.763 3.86 3.86 0 00.099-.874zM12.714 9.823l-2.353-3.22h1.814l1.42 2.09 1.485-2.09h1.735l-2.313 3.141 2.602 3.654h-1.801l-1.721-2.51-1.67 2.51h-1.761l2.563-3.575z"></path>
	</g>
  </svg>,
	em: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<g fillRule="nonzero">
	  <path d="M6.248 9.402a2.401 2.401 0 00-.152-.683 1.704 1.704 0 00-.867-.967 1.56 1.56 0 00-.69-.151c-.263 0-.502.046-.716.138a1.633 1.633 0 00-.552.381 1.886 1.886 0 00-.368.572 2.002 2.002 0 00-.152.71h3.497zm-3.497.986c0 .263.038.517.112.762.**************.335.644.149.184.338.331.565.***********.165.815.165.438 0 .791-.095 1.058-.283.268-.188.467-.471.598-.848h1.42a2.824 2.824 0 01-1.104 1.716 2.99 2.99 0 01-.914.446c-.337.101-.69.152-1.058.152-.534 0-1.007-.088-1.419-.263a2.95 2.95 0 01-1.045-.736 3.12 3.12 0 01-.644-1.131 4.577 4.577 0 01-.217-1.445c0-.482.077-.94.23-1.374.153-.434.372-.815.657-1.143a3.13 3.13 0 011.032-.782c.403-.193.859-.29 1.367-.29a3.04 3.04 0 011.439.336c.425.223.778.519 1.058.887.281.368.484.791.611 1.268.127.478.16.971.099 1.479H2.751zM8.889 6.602h1.42v.947h.039c.114-.167.237-.32.368-.46a2.022 2.022 0 01.999-.585c.211-.057.451-.086.723-.086.412 0 .795.092 1.15.276.355.184.607.469.756.855a3.01 3.01 0 01.881-.828c.333-.202.749-.303 1.248-.303.719 0 1.277.176 1.676.526.399.351.598.938.598 1.761v4.693h-1.498v-3.97c0-.271-.009-.519-.027-.742a1.512 1.512 0 00-.151-.579.856.856 0 00-.374-.374c-.167-.088-.395-.132-.684-.132-.508 0-.876.158-1.104.473-.228.316-.342.763-.342 1.341v3.983h-1.498V9.034c0-.473-.085-.83-.256-1.071-.171-.241-.484-.362-.94-.362-.193 0-.379.04-.559.119a1.385 1.385 0 00-.473.341 1.703 1.703 0 00-.328.552 2.084 2.084 0 00-.125.75v4.035H8.889V6.602z"></path>
	</g>
  </svg>,
  percent: <svg width="24" height="24"
  xmlns="http://www.w3.org/2000/svg"
  fillRule="evenodd"
  strokeLinejoin="round"
  strokeMiterlimit="2"
  clipRule="evenodd"
  viewBox="0 0 20 20"
>
  <path
	fillRule="nonzero"
	d="M5.689 7.831c0 .246.017.476.**************.092.401.17.559.079.158.182.283.309.375a.775.775 0 00.467.138.803.803 0 00.473-.138.978.978 0 00.315-.375 2.11 2.11 0 00.178-.559c.039-.214.059-.444.059-.69 0-.219-.015-.433-.046-.644a1.995 1.995 0 00-.164-.565 1.076 1.076 0 00-.316-.401.794.794 0 00-.499-.151.797.797 0 00-.5.151 1.02 1.02 0 00-.308.401 1.992 1.992 0 00-.152.565 5.253 5.253 0 00-.039.644zm1.012 2.616c-.394 0-.732-.07-1.012-.21a1.899 1.899 0 01-.684-.566 2.316 2.316 0 01-.381-.828 4.148 4.148 0 01-.118-1.012c0-.35.042-.685.125-1.005.083-.32.215-.598.394-.835.18-.236.408-.425.684-.565.276-.14.606-.21.992-.21s.716.07.992.21c.276.14.504.329.684.565.179.237.311.515.394.835.083.32.125.655.125 1.005 0 .36-.039.697-.118 1.012a2.3 2.3 0 01-.382.828 1.887 1.887 0 01-.683.566c-.28.14-.618.21-1.012.21zm5.586 1.722c0 .245.017.475.**************.092.401.17.558.079.158.182.283.309.375a.775.775 0 00.467.138.803.803 0 00.473-.138.978.978 0 00.315-.375c.079-.157.138-.344.178-.558.039-.215.059-.445.059-.69 0-.219-.015-.434-.046-.644a1.992 1.992 0 00-.164-.566 1.065 1.065 0 00-.316-.4.795.795 0 00-.499-.152.798.798 0 00-.5.152 1.01 1.01 0 00-.308.4 1.99 1.99 0 00-.152.566c-.026.21-.039.425-.039.644zm1.012 2.615c-.394 0-.732-.07-1.012-.21a1.885 1.885 0 01-.683-.565 2.317 2.317 0 01-.382-.828 4.16 4.16 0 01-.118-1.012c0-.351.042-.686.125-1.006.083-.32.215-.598.394-.834.18-.237.408-.425.684-.566.276-.14.606-.21.992-.21s.716.07.992.21c.276.141.504.329.684.566.179.236.311.514.394.834.083.32.125.655.125 1.006 0 .359-.039.696-.118 1.012a2.332 2.332 0 01-.381.828 1.897 1.897 0 01-.684.565c-.28.14-.618.21-1.012.21zm-1.341-9.7h.999l-5.086 9.832H6.846l5.112-9.832z"
  ></path>
</svg>,
rem: <svg width="24" height="24"
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<g fillRule="nonzero">
  <path d="M.731 7.079H1.94v1.13h.023c.038-.158.111-.313.22-.463.11-.151.241-.289.396-.413.154-.124.326-.224.514-.3.189-.075.381-.113.577-.113.151 0 .254.004.311.012l.175.022v1.244a5.951 5.951 0 00-.277-.04 2.393 2.393 0 00-.277-.017 1.424 1.424 0 00-1.119.514c-.143.17-.256.379-.339.628a2.712 2.712 0 00-.125.859v2.781H.731V7.079zM8.519 9.486a2.057 2.057 0 00-.13-.587 1.534 1.534 0 00-.294-.492 1.433 1.433 0 00-.452-.339 1.327 1.327 0 00-.593-.13c-.226 0-.432.039-.616.118-.185.08-.343.189-.475.328-.132.14-.238.304-.317.492a1.727 1.727 0 00-.13.61h3.007zm-3.007.848c0 .226.032.445.096.656.064.211.161.395.289.554.128.158.29.284.486.378.196.095.429.142.701.142.376 0 .68-.081.91-.243.229-.162.401-.405.514-.73h1.221a2.422 2.422 0 01-.95 1.476 2.597 2.597 0 01-.785.384c-.291.087-.594.13-.91.13-.46 0-.867-.075-1.221-.226a2.535 2.535 0 01-.899-.633 2.69 2.69 0 01-.554-.972 3.964 3.964 0 01-.186-1.244c0-.414.066-.808.198-1.181.131-.373.32-.701.565-.983.245-.283.54-.507.887-.673A2.692 2.692 0 017.05 6.92c.459 0 .872.096 1.237.289.366.192.669.446.91.763.242.316.417.68.526 1.09.109.411.138.835.085 1.272H5.512zM10.791 7.079h1.221v.813h.034c.098-.143.203-.275.317-.395a1.722 1.722 0 01.859-.503c.18-.049.388-.074.621-.074.355 0 .684.079.989.238.306.158.522.403.65.734.219-.301.471-.538.758-.712.286-.173.644-.26 1.074-.26.618 0 1.098.151 1.441.452.343.302.514.807.514 1.515v4.036h-1.288V9.509c0-.234-.008-.447-.023-.639a1.292 1.292 0 00-.13-.497.737.737 0 00-.322-.322c-.143-.076-.339-.113-.588-.113-.437 0-.754.135-.95.407-.195.271-.293.655-.293 1.153v3.425h-1.289V9.17c0-.407-.074-.714-.221-.921-.146-.208-.416-.311-.808-.311a1.192 1.192 0 00-.887.395 1.48 1.48 0 00-.283.475 1.815 1.815 0 00-.107.644v3.471h-1.289V7.079z"></path>
</g>
</svg>,
vh: <svg width="24" height="24"
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<g fillRule="nonzero">
  <path d="M3.271 6.603H4.9l1.722 5.218h.026l1.656-5.218h1.551l-2.431 6.795H5.742L3.271 6.603zM10.762 4.014h1.499v3.483h.026c.184-.307.458-.563.821-.769a2.425 2.425 0 011.216-.309c.745 0 1.332.193 1.761.578.43.386.644.964.644 1.735v4.666h-1.498V9.127c-.017-.535-.131-.923-.342-1.164-.21-.241-.538-.361-.985-.361-.254 0-.482.046-.684.138a1.484 1.484 0 00-.512.381c-.141.162-.25.353-.329.572a2.035 2.035 0 00-.118.696v4.009h-1.499V4.014z"></path>
</g>
</svg>,
vw: <svg width="24" height="24"
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<g fillRule="nonzero">
  <path d="M1.621 6.603h1.63l1.722 5.218h.026l1.656-5.218h1.551l-2.432 6.795H4.092L1.621 6.603zM8.495 6.603h1.59l1.328 5.073h.026l1.275-5.073h1.512l1.222 5.073h.026l1.38-5.073h1.525l-2.129 6.795h-1.538L13.45 8.351h-.026l-1.249 5.047h-1.577L8.495 6.603z"></path>
</g>
</svg>,
	none: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<path d="M17.401 4.69L15.31 2.599 2.599 15.31l2.091 2.091L17.401 4.69z"></path>
	<path d="M4.69 2.599L2.599 4.69 15.31 17.401l2.091-2.091L4.69 2.599z"></path>
  </svg>,
	solid: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<path d="M18.988 11.478V8.522H1.012v2.956h17.976z"></path>
  </svg>,
	dashed: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<path d="M12.512 11.478V8.522H7.488v2.956h5.024zM14.004 8.522v2.956h4.984V8.522h-4.984zM1.012 8.522v2.956H6.05V8.522H1.012z"></path>
  </svg>,
	dotted: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<circle cx="2.503" cy="10" r="1.487"></circle>
	<circle cx="17.486" cy="10" r="1.487"></circle>
	<circle cx="12.447" cy="10" r="1.487"></circle>
	<circle cx="7.455" cy="10" r="1.487"></circle>
  </svg>,
	double: <svg width="24" height="24"
	xmlns="http://www.w3.org/2000/svg"
	fillRule="evenodd"
	strokeLinejoin="round"
	strokeMiterlimit="2"
	clipRule="evenodd"
	viewBox="0 0 20 20"
  >
	<path d="M1.02 6.561v2.957h17.968V6.561H1.02zM1.012 10.586v2.956H18.98v-2.956H1.012z"></path>
  </svg>,
  lowercase: <svg width="24" height="24"
  xmlns="http://www.w3.org/2000/svg"
  fillRule="evenodd"
  strokeLinejoin="round"
  strokeMiterlimit="2"
  clipRule="evenodd"
  viewBox="0 0 20 20"
>
  <g fillRule="nonzero">
	<path d="M9.1 13.181c0 .184.024.315.072.394.***************.283.118h.157a.953.953 0 00.211-.026v1.038a3.222 3.222 0 01-.204.06 3.035 3.035 0 01-.743.111c-.306 0-.561-.061-.762-.184-.202-.122-.333-.337-.394-.644-.298.289-.664.5-1.098.631a4.303 4.303 0 01-1.255.197c-.307 0-.6-.041-.881-.125a2.302 2.302 0 01-.742-.368 1.764 1.764 0 01-.513-.617 1.904 1.904 0 01-.19-.874c0-.421.076-.763.23-1.026.153-.262.354-.468.604-.617.25-.149.53-.257.841-.322.311-.066.625-.116.94-.152.272-.052.53-.089.776-.111a3.6 3.6 0 00.65-.112c.189-.053.337-.134.447-.243.11-.11.164-.274.164-.493a.765.765 0 00-.138-.473.931.931 0 00-.341-.283 1.5 1.5 0 00-.454-.131 3.61 3.61 0 00-.473-.033c-.421 0-.767.088-1.038.263-.272.175-.425.447-.46.815H3.29c.027-.438.132-.802.316-1.091.184-.289.418-.522.703-.697.285-.175.607-.298.966-.368s.727-.105 1.104-.105c.333 0 .662.035.986.105.324.07.615.184.874.342.258.158.466.361.624.611.158.25.237.554.237.914v3.496zm-1.499-1.893a1.822 1.822 0 01-.841.27 10.7 10.7 0 00-.999.138 3.18 3.18 0 00-.46.111c-.149.048-.28.114-.394.197a.874.874 0 00-.27.329c-.065.136-.098.3-.098.493 0 .166.048.307.144.42.097.114.213.204.349.27.136.066.285.112.447.138.162.026.309.039.44.039.166 0 .346-.022.539-.065.193-.044.374-.119.545-.224.171-.105.314-.239.427-.401.114-.162.171-.361.171-.598v-1.117zM10.756 5.308h1.498v3.47h.026c.106-.167.233-.316.382-.447.149-.132.313-.243.492-.335a2.62 2.62 0 01.566-.211c.197-.048.392-.072.584-.072.535 0 1.002.094 1.4.283.399.188.73.446.993.775.262.329.46.712.591 1.15.132.438.197.907.197 1.406 0 .456-.059.899-.177 1.328a3.475 3.475 0 01-.539 1.137 2.713 2.713 0 01-.914.789c-.368.197-.801.295-1.301.295-.228 0-.458-.015-.69-.046a2.614 2.614 0 01-.664-.177c-.21-.088-.4-.202-.571-.342a1.645 1.645 0 01-.427-.552h-.027v.933h-1.419V5.308zm5.231 5.993c0-.306-.04-.604-.118-.894a2.44 2.44 0 00-.355-.768 1.818 1.818 0 00-.592-.539 1.604 1.604 0 00-.815-.204c-.631 0-1.106.219-1.426.657-.32.438-.479 1.021-.479 1.748 0 .342.041.66.124.953.084.294.209.546.375.756.166.21.366.377.598.499.232.123.502.184.808.184.342 0 .631-.07.868-.21.236-.14.431-.322.585-.545a2.21 2.21 0 00.328-.763 3.86 3.86 0 00.099-.874z"></path>
  </g>
</svg>,
uppercase: <svg width="24" height="24"
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<g fillRule="nonzero">
  <path d="M4.789 5.308h1.748l3.614 9.384H8.39l-.881-2.484H3.763l-.88 2.484H1.187l3.602-9.384zm-.579 5.651h2.866L5.669 6.924H5.63l-1.42 4.035zM12.688 9.238h2.681c.394 0 .723-.112.986-.335.263-.224.394-.546.394-.967 0-.473-.118-.806-.355-.998-.236-.193-.578-.29-1.025-.29h-2.681v2.59zm-1.643-3.93h4.561c.841 0 1.516.193 2.024.578.508.386.762.968.762 1.748 0 .473-.116.879-.348 1.216-.232.337-.563.598-.993.782v.026c.579.123 1.017.397 1.315.822.298.425.447.957.447 1.597 0 .368-.066.712-.197 1.031a2.11 2.11 0 01-.618.828c-.281.233-.64.417-1.078.553-.438.135-.959.203-1.564.203h-4.311V5.308zm1.643 8.044h2.905c.499 0 .887-.13 1.163-.388.276-.259.414-.624.414-1.098 0-.464-.138-.821-.414-1.071-.276-.25-.664-.374-1.163-.374h-2.905v2.931z"></path>
</g>
</svg>,
capitalize: <svg width="24" height="24"
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<g fillRule="nonzero">
  <path d="M5.393 5.216h1.748l3.615 9.384H8.995l-.881-2.484H4.368L3.487 14.6H1.792l3.601-9.384zm-.578 5.651H7.68L6.274 6.832h-.04l-1.419 4.035zM11.479 5.216h1.498v3.47h.026c.105-.167.232-.316.381-.447a2.38 2.38 0 01.493-.335 2.62 2.62 0 01.566-.211c.197-.048.392-.072.584-.072.535 0 1.001.094 1.4.283.399.188.73.446.993.775.262.329.46.712.591 1.15.131.438.197.907.197 1.406 0 .456-.059.899-.177 1.328a3.475 3.475 0 01-.539 1.137 2.713 2.713 0 01-.914.789c-.368.197-.801.295-1.301.295-.228 0-.458-.015-.69-.046a2.614 2.614 0 01-.664-.177 2.278 2.278 0 01-.571-.342 1.66 1.66 0 01-.428-.552h-.026v.933h-1.419V5.216zm5.231 5.993c0-.306-.04-.604-.119-.894a2.416 2.416 0 00-.354-.768 1.818 1.818 0 00-.592-.539 1.604 1.604 0 00-.815-.204c-.631 0-1.106.219-1.426.657-.32.438-.48 1.021-.48 1.748 0 .342.042.66.125.953.084.294.208.546.375.756.166.21.366.377.598.499.232.123.502.184.808.184.342 0 .631-.07.868-.21.236-.14.431-.322.585-.545.153-.224.262-.478.328-.763a3.86 3.86 0 00.099-.874z"></path>
</g>
</svg>,
menu: <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M3 13h18a1 1 0 000-2H3a1 1 0 000 2zm0-6h18a1 1 0 000-2H3a1 1 0 000 2zm0 12h18a1 1 0 000-2H3a1 1 0 000 2z"></path>
</svg>,
menu2:  <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="28"
viewBox="0 0 24 28"
>
<path d="M24 21v2c0 .547-.453 1-1 1H1c-.547 0-1-.453-1-1v-2c0-.547.453-1 1-1h22c.547 0 1 .453 1 1zm0-8v2c0 .547-.453 1-1 1H1c-.547 0-1-.453-1-1v-2c0-.547.453-1 1-1h22c.547 0 1 .453 1 1zm0-8v2c0 .547-.453 1-1 1H1c-.547 0-1-.453-1-1V5c0-.547.453-1 1-1h22c.547 0 1 .453 1 1z"></path>
</svg>,
menu3: <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M6 3a2 2 0 113.999-.001A2 2 0 016 3zm0 5a2 2 0 113.999-.001A2 2 0 016 8zm0 5a2 2 0 113.999-.001A2 2 0 016 13z"></path>
</svg>,
unlocked: <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M5 12h14c.276 0 .525.111.707.293S20 12.724 20 13v7c0 .276-.111.525-.293.707S19.276 21 19 21H5c-.276 0-.525-.111-.707-.293S4 20.276 4 20v-7c0-.276.111-.525.293-.707S4.724 12 5 12zm3-2V7a3.988 3.988 0 011.169-2.831 3.983 3.983 0 012.821-1.174 3.985 3.985 0 012.652 1 4.052 4.052 0 011.28 2.209 1 1 0 101.958-.408 6.051 6.051 0 00-1.912-3.299A5.963 5.963 0 0011.995.995c-1.657.002-3.157.676-4.241 1.762S5.998 5.344 6 7v3H5a2.997 2.997 0 00-3 3v7a2.997 2.997 0 003 3h14a2.997 2.997 0 003-3v-7a2.997 2.997 0 00-3-3z"></path>
</svg>,
locked: <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M5 12h14c.276 0 .525.111.707.293S20 12.724 20 13v7c0 .276-.111.525-.293.707S19.276 21 19 21H5c-.276 0-.525-.111-.707-.293S4 20.276 4 20v-7c0-.276.111-.525.293-.707S4.724 12 5 12zm13-2V7c0-1.657-.673-3.158-1.757-4.243S13.657 1 12 1s-3.158.673-4.243 1.757S6 5.343 6 7v3H5a2.997 2.997 0 00-3 3v7a2.997 2.997 0 003 3h14a2.997 2.997 0 003-3v-7a2.997 2.997 0 00-3-3zM8 10V7c0-1.105.447-2.103 1.172-2.828S10.895 3 12 3s2.103.447 2.828 1.172S16 5.895 16 7v3z"></path>
</svg>,
fullwidth: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path
  fill="#cdcdcd"
  d="M1.19 1.153H48.809999999999995V12.969000000000001H1.19z"
></path>
<path
  fill="#ccc"
  fillRule="nonzero"
  d="M37.149 34.831H2.714c-.411 0-.749.433-.749.959s.338.959.749.959h34.435c.411 0 .749-.433.749-.959s-.338-.959-.749-.959zM47.038 30.979H2.924a.964.964 0 00-.959.959c0 .527.433.959.959.959h44.114a.963.963 0 00.959-.959.964.964 0 00-.959-.959z"
></path>
<path
  fill="#cdcdcd"
  fillRule="nonzero"
  d="M47.038 27.128H2.924a.964.964 0 00-.959.959c0 .526.433.959.959.959h44.114a.964.964 0 00.959-.959.964.964 0 00-.959-.959zM47.038 23.277H2.924a.964.964 0 00-.959.959c0 .526.433.959.959.959h44.114a.964.964 0 00.959-.959.964.964 0 00-.959-.959z"
></path>
<path
  fill="#ccc"
  fillRule="nonzero"
  d="M47.038 19.426H2.924a.964.964 0 00-.959.959c0 .526.433.959.959.959h44.114a.964.964 0 00.959-.959.964.964 0 00-.959-.959zM47.038 15.575H2.924a.964.964 0 00-.959.959c0 .526.433.959.959.959h44.114a.964.964 0 00.959-.959.964.964 0 00-.959-.959z"
></path>
<path
  fill="none"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
</svg>,
normal: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path
  fill="#cdcdcd"
  d="M7.49 3.877H42.521V13.879000000000001H7.49z"
></path>
<path
  fill="#ccc"
  fillRule="nonzero"
  d="M34.237 34.498H8.049c-.313 0-.57.41-.57.908 0 .497.257.907.57.907h26.188c.313 0 .57-.41.57-.907 0-.498-.257-.908-.57-.908zM41.776 30.855H8.226c-.4 0-.729.409-.729.907 0 .498.329.907.729.907h33.55c.399 0 .729-.409.729-.907 0-.498-.33-.907-.729-.907z"
></path>
<path
  fill="#cdcdcd"
  fillRule="nonzero"
  d="M41.776 27.211H8.226c-.4 0-.729.41-.729.908 0 .497.329.907.729.907h33.55c.399 0 .729-.41.729-.907 0-.498-.33-.908-.729-.908zM41.776 23.568H8.226c-.4 0-.729.41-.729.907 0 .498.329.908.729.908h33.55c.399 0 .729-.41.729-.908 0-.497-.33-.907-.729-.907z"
></path>
<path
  fill="#ccc"
  fillRule="nonzero"
  d="M41.776 19.925H8.226c-.4 0-.729.409-.729.907 0 .498.329.907.729.907h33.55c.399 0 .729-.409.729-.907 0-.498-.33-.907-.729-.907zM41.776 16.281H8.226c-.4 0-.729.41-.729.907 0 .498.329.908.729.908h33.55c.399 0 .729-.41.729-.908 0-.497-.33-.907-.729-.907z"
></path>
</svg>,
narrow:<svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path
  fill="#cdcdcd"
  d="M12.511 3.877H37.497V13.879000000000001H12.511z"
></path>
<path
  fill="#ccc"
  fillRule="nonzero"
  d="M31.588 34.498H12.91c-.223 0-.407.41-.407.908 0 .497.184.907.407.907h18.678c.224 0 .407-.41.407-.907 0-.498-.183-.908-.407-.908zM36.965 30.855H13.036c-.285 0-.52.409-.52.907 0 .498.235.907.52.907h23.929c.285 0 .52-.409.52-.907 0-.498-.235-.907-.52-.907z"
></path>
<path
  fill="#cdcdcd"
  fillRule="nonzero"
  d="M36.965 27.211H13.036c-.285 0-.52.41-.52.908 0 .497.235.907.52.907h23.929c.285 0 .52-.41.52-.907 0-.498-.235-.908-.52-.908zM36.965 23.568H13.036c-.285 0-.52.41-.52.907 0 .498.235.908.52.908h23.929c.285 0 .52-.41.52-.908 0-.497-.235-.907-.52-.907z"
></path>
<path
  fill="#ccc"
  fillRule="nonzero"
  d="M36.965 19.925H13.036c-.285 0-.52.409-.52.907 0 .498.235.907.52.907h23.929c.285 0 .52-.409.52-.907 0-.498-.235-.907-.52-.907zM36.965 16.281H13.036c-.285 0-.52.41-.52.907 0 .498.235.908.52.908h23.929c.285 0 .52-.41.52-.908 0-.497-.235-.907-.52-.907z"
></path>
</svg>,
rightsidebar: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path
  fill="#cdcdcd"
  d="M7.516 3.855H30.216V13.857000000000001H7.516z"
></path>
<g fillRule="nonzero">
  <path
	fill="#ccc"
	d="M7.874 34.421h16.987c.202 0 .369.409.369.907 0 .498-.167.907-.369.907H7.874c-.202 0-.369-.409-.369-.907 0-.498.167-.907.369-.907zM7.98 30.777h21.76c.26 0 .473.41.473.908 0 .497-.213.907-.473.907H7.98c-.26 0-.473-.41-.473-.907 0-.498.213-.908.473-.908z"
  ></path>
  <path
	fill="#cdcdcd"
	d="M7.98 27.134h21.76c.26 0 .473.41.473.907 0 .498-.213.907-.473.907H7.98c-.26 0-.473-.409-.473-.907 0-.497.213-.907.473-.907zM7.98 23.491h21.76c.26 0 .473.409.473.907 0 .498-.213.907-.473.907H7.98c-.26 0-.473-.409-.473-.907 0-.498.213-.907.473-.907z"
  ></path>
  <path
	fill="#ccc"
	d="M7.98 19.847h21.76c.26 0 .473.41.473.907 0 .498-.213.908-.473.908H7.98c-.26 0-.473-.41-.473-.908 0-.497.213-.907.473-.907zM7.98 16.204h21.76c.26 0 .473.409.473.907 0 .498-.213.907-.473.907H7.98c-.26 0-.473-.409-.473-.907 0-.498.213-.907.473-.907z"
  ></path>
</g>
<path
  fill="#e5e5e5"
  d="M32.602 3.892H42.492999999999995V36.143H32.602z"
></path>
</svg>,
leftsidebar:<svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path
  fill="#cdcdcd"
  d="M19.784 3.855H42.483999999999995V13.857000000000001H19.784z"
></path>
<g fillRule="nonzero">
  <path
	fill="#ccc"
	d="M37.131 34.421H20.145c-.203 0-.37.409-.37.907 0 .498.167.907.37.907h16.986c.202 0 .369-.409.369-.907 0-.498-.167-.907-.369-.907zM42.02 30.777H20.26c-.26 0-.473.41-.473.908 0 .497.213.907.473.907h21.76c.26 0 .473-.41.473-.907 0-.498-.213-.908-.473-.908z"
  ></path>
  <path
	fill="#cdcdcd"
	d="M42.02 27.134H20.26c-.26 0-.473.41-.473.907 0 .498.213.907.473.907h21.76c.26 0 .473-.409.473-.907 0-.497-.213-.907-.473-.907zM42.02 23.491H20.26c-.26 0-.473.409-.473.907 0 .498.213.907.473.907h21.76c.26 0 .473-.409.473-.907 0-.498-.213-.907-.473-.907z"
  ></path>
  <path
	fill="#ccc"
	d="M42.02 19.847H20.26c-.26 0-.473.41-.473.907 0 .498.213.908.473.908h21.76c.26 0 .473-.41.473-.908 0-.497-.213-.907-.473-.907zM42.02 16.204H20.26c-.26 0-.473.409-.473.907 0 .498.213.907.473.907h21.76c.26 0 .473-.409.473-.907 0-.498-.213-.907-.473-.907z"
  ></path>
</g>
<path fill="#e5e5e5" d="M7.507 3.892H17.398V36.143H7.507z"></path>
</svg>,
abovecontent: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path
  fill="#cdcdcd"
  d="M1.19 1.153H48.809999999999995V12.969000000000001H1.19z"
></path>
<g fillRule="nonzero">
  <path
	fill="#ccc"
	d="M34.26 34.831H8.054c-.312 0-.569.433-.569.959s.257.959.569.959H34.26c.313 0 .57-.433.57-.959s-.257-.959-.57-.959zM41.785 30.979H8.215c-.401 0-.73.433-.73.959 0 .527.329.959.73.959h33.57c.401 0 .73-.432.73-.959 0-.526-.329-.959-.73-.959z"
  ></path>
  <path
	fill="#cdcdcd"
	d="M41.785 27.128H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959zM41.785 23.277H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959z"
  ></path>
  <path
	fill="#ccc"
	d="M41.785 19.426H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959zM41.785 15.575H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959z"
  ></path>
</g>
<path
  fill="#fff"
  fillRule="nonzero"
  d="M38.103 6.869H11.897c-.312 0-.569.433-.569.959 0 .527.257.959.569.959h26.206c.312 0 .569-.432.569-.959 0-.526-.257-.959-.569-.959zM31.143 4.758H18.857c-.147 0-.267.269-.267.595 0 .327.12.595.267.595h12.286c.147 0 .267-.268.267-.595 0-.326-.12-.595-.267-.595z"
></path>
<path
  fill="none"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
</svg>,
incontent: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<g fillRule="nonzero">
  <path
	fill="#ccc"
	d="M34.26 32.335H8.054c-.312 0-.569.433-.569.959s.257.959.569.959H34.26c.313 0 .57-.433.57-.959s-.257-.959-.57-.959zM41.785 28.484H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959z"
  ></path>
  <path
	fill="#cdcdcd"
	d="M41.785 24.632H8.215c-.401 0-.73.433-.73.959 0 .527.329.959.73.959h33.57c.401 0 .73-.432.73-.959 0-.526-.329-.959-.73-.959zM41.785 20.781H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959z"
  ></path>
  <path
	fill="#ccc"
	d="M41.785 16.93H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959zM41.785 13.079H8.215c-.401 0-.73.433-.73.959s.329.959.73.959h33.57c.401 0 .73-.433.73-.959s-.329-.959-.73-.959z"
  ></path>
  <path
	fill="#9a9a9a"
	d="M34.26 7.888H8.054c-.312 0-.569.433-.569.959s.257.959.569.959H34.26c.313 0 .57-.433.57-.959s-.257-.959-.57-.959zM19.303 5.747H7.736c-.138 0-.251.265-.251.586 0 .321.113.586.251.586h11.567c.138 0 .252-.265.252-.586 0-.321-.114-.586-.252-.586z"
  ></path>
</g>
<path
  fill="none"
  stroke="#9a9a9a"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
</svg>,
search: <svg
xmlns="http://www.w3.org/2000/svg"
width="26"
height="28"
viewBox="0 0 26 28"
>
<path d="M18 13c0-3.859-3.141-7-7-7s-7 3.141-7 7 3.141 7 7 7 7-3.141 7-7zm8 13c0 1.094-.906 2-2 2a1.96 1.96 0 01-1.406-.594l-5.359-5.344a10.971 10.971 0 01-6.234 1.937c-6.078 0-11-4.922-11-11s4.922-11 11-11 11 4.922 11 11c0 2.219-.672 4.406-1.937 6.234l5.359 5.359c.359.359.578.875.578 1.406z"></path>
</svg>,
search2: <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M16.041 15.856a.995.995 0 00-.186.186A6.97 6.97 0 0111 18c-1.933 0-3.682-.782-4.95-2.05S4 12.933 4 11s.782-3.682 2.05-4.95S9.067 4 11 4s3.682.782 4.95 2.05S18 9.067 18 11a6.971 6.971 0 01-1.959 4.856zm5.666 4.437l-3.675-3.675A8.967 8.967 0 0020 11c0-2.485-1.008-4.736-2.636-6.364S13.485 2 11 2 6.264 3.008 4.636 4.636 2 8.515 2 11s1.008 4.736 2.636 6.364S8.515 20 11 20a8.967 8.967 0 005.618-1.968l3.675 3.675a.999.999 0 101.414-1.414z"></path>
</svg>,
dot: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<circle cx="10" cy="10" r="4.942"></circle>
</svg>,
vline: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<path fillRule="nonzero" d="M9.022 1.068H10.977V18.931H9.022z"></path>
</svg>,
slash: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<path
  fillRule="nonzero"
  d="M6.115 18.935l5.804-17.87h1.966l-5.79 17.87h-1.98z"
></path>
</svg>,
dash: <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 20 20"
>
<path fillRule="nonzero" d="M3.851 8.065H16.148V11.934H3.851z"></path>
</svg>,
drag: <svg
		width="18"
		height="18"
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 18 18"
	>
		<path
			d="M13,8c0.6,0,1-0.4,1-1s-0.4-1-1-1s-1,0.4-1,1S12.4,8,13,8z M5,6C4.4,6,4,6.4,4,7s0.4,1,1,1s1-0.4,1-1S5.6,6,5,6z M5,10
			c-0.6,0-1,0.4-1,1s0.4,1,1,1s1-0.4,1-1S5.6,10,5,10z M13,10c-0.6,0-1,0.4-1,1s0.4,1,1,1s1-0.4,1-1S13.6,10,13,10z M9,6
			C8.4,6,8,6.4,8,7s0.4,1,1,1s1-0.4,1-1S9.6,6,9,6z M9,10c-0.6,0-1,0.4-1,1s0.4,1,1,1s1-0.4,1-1S9.6,10,9,10z"
		/>
	</svg>
};
Icons.row = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
	clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="0.000" y="0.000" width="60.000" height="30.000" fill="#d5dadf"
	/>
</svg>;
Icons.collapserow = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
	clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="0.000" y="0.000" width="60.000" height="14.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="16.000" width="60.000" height="14.000" fill="#d5dadf"
	/>
</svg>;
Icons.collapserowthree = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
	clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="0.000" y="0.000" width="60.000" height="9.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="10.500" width="60.000" height="9.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="21.000" width="60.000" height="9.000" fill="#d5dadf"
	/>
</svg>;
Icons.collapserowfour = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
	clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="0.000" y="0.000" width="60.000" height="6.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="8.000" width="60.000" height="6.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="16.000" width="60.000" height="6.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="24.000" width="60.000" height="6.000" fill="#d5dadf"
	/>
</svg>;
Icons.collapserowfive = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
	clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="0.000" y="0.000" width="60.000" height="5.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="6.000" width="60.000" height="5.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="12.000" width="60.000" height="5.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="18.000" width="60.000" height="5.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="24.000" width="60.000" height="5.000" fill="#d5dadf"
	/>
</svg>;
Icons.collapserowsix = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="0.000" y="0.000" width="60.000" height="4.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="5.000" width="60.000" height="4.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="10.000" width="60.000" height="4.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="15.000" width="60.000" height="4.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="20.000" width="60.000" height="4.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="25.000" width="60.000" height="4.000" fill="#d5dadf"
	/>
</svg>;
Icons.twocol = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
	clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
	<rect x="31.000" y="0.000" width="29.000" height="30.000" fill="#d5dadf"
	/>
	<rect x="0.000" y="0.000" width="29.000" height="30.000" fill="#d5dadf"
	/>
</svg>;
Icons.grid = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="31.000" y="0.000" width="29.000" height="14.000" fill="#d5dadf"
/>
<rect x="0.000" y="0.000" width="29.000" height="14.000" fill="#d5dadf"
/>
<rect x="31.000" y="16.000" width="29.000" height="14.000" fill="#d5dadf"
/>
<rect x="0.000" y="16.000" width="29.000" height="14.000" fill="#d5dadf"
/>
</svg>;
Icons.threecol = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="18.500" height="30.000" fill="#d5dadf"
/>
<rect x="20.500" y="0.000" width="19.000" height="30.000" fill="#d5dadf"
/>
<rect x="41.500" y="0.000" width="18.500" height="30.000" fill="#d5dadf"
/>
</svg>;
Icons.threegrid = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="18.500" height="14.000" fill="#d5dadf"
/>
<rect x="20.500" y="0.000" width="19.000" height="14.000" fill="#d5dadf"
/>
<rect x="41.500" y="0.000" width="18.500" height="14.000" fill="#d5dadf"
/>
<rect x="0.000" y="16.000" width="18.500" height="14.000" fill="#d5dadf"
/>
<rect x="20.500" y="16.000" width="19.000" height="14.000" fill="#d5dadf"
/>
<rect x="41.500" y="16.000" width="18.500" height="14.000" fill="#d5dadf"
/>
</svg>;
Icons.lastrow = <svg viewBox='0 0 60 30' xmlns='http://www.w3.org/2000/svg' fillRule='evenodd'
clipRule='evenodd' strokeLinejoin='round' strokeMiterlimit='1.414'>
  <rect x='31' width='29' height='14' fill='#d5dadf' />
  <rect x='-0.024' width='29' height='14' fill='#d5dadf' />
  <rect x='-0.024' y='16' width='60' height='14' fill='#d5dadf' />
</svg>;
Icons.firstrow = <svg viewBox='0 0 60 30' xmlns='http://www.w3.org/2000/svg' fillRule='evenodd'
clipRule='evenodd' strokeLinejoin='round' strokeMiterlimit='1.414'>
  <rect x='31' y='16' width='29' height='14' fill='#d5dadf' />
  <rect x='-0.024' y='16' width='29' height='14' fill='#d5dadf' />
  <rect x='-0.024' y='-0.003' width='60' height='14' fill='#d5dadf' />
</svg>;
Icons.twoleftgolden = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="41.000" y="0.000" width="19.000" height="30.000" fill="#d5dadf"
/>
<rect x="0.000" y="0.000" width="39.000" height="30.000" fill="#d5dadf"
/>
</svg>;
Icons.tworightgolden = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="21.000" y="0.000" width="39.000" height="30.000" fill="#d5dadf"
/>
<rect x="0.000" y="0.000" width="19.000" height="30.000" fill="#d5dadf"
/>
</svg>;
Icons.lefthalf = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="29.000" height="30.000" fill="#d5dadf"
/>
<rect x="31" y="0.000" width="13.500" height="30.000" fill="#d5dadf"
/>
<rect x="46.500" y="0.000" width="13.500" height="30.000" fill="#d5dadf"
/>
</svg>;
Icons.righthalf = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="13.500" height="30.000" fill="#d5dadf"
/>
<rect x="15.500" y="0.000" width="13.500" height="30.000" fill="#d5dadf"
/>
<rect x="31.000" y="0.000" width="29.000" height="30.000" fill="#d5dadf"
/>
</svg>;
Icons.centerhalf = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd"
clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="13.500" height="30.000" fill="#d5dadf"
/>
<rect x="15.500" y="0.000" width="29.000" height="30.000" fill="#d5dadf"
/>
<rect x="46.500" y="0.000" width="13.500" height="30.000" fill="#d5dadf"
/>
</svg>;
Icons.widecenter = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="11.000" height="30.000" fill="#d5dadf" />
<rect x="13.000" y="0.000" width="34.000" height="30.000" fill="#d5dadf" />
<rect x="49.000" y="0.000" width="11.000" height="30.000" fill="#d5dadf" />
</svg>;
Icons.exwidecenter = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="7.200" height="30.000" fill="#d5dadf" />
<rect x="9.200" y="0.000" width="41.600" height="30.000" fill="#d5dadf" />
<rect x="52.800" y="0.000" width="7.200" height="30.000" fill="#d5dadf" />
</svg>;
Icons.fourcol = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="13.500" height="30.000" fill="#d5dadf" />
<rect x="15.500" y="0.000" width="13.500" height="30.000" fill="#d5dadf" />
<rect x="31.000" y="0.000" width="13.500" height="30.000" fill="#d5dadf" />
<rect x="46.500" y="0.000" width="13.500" height="30.000" fill="#d5dadf" />
</svg>;
Icons.lfourforty = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="21.600" height="30.000" fill="#d5dadf" />
<rect x="23.600" y="0.000" width="10.800" height="30.000" fill="#d5dadf" />
<rect x="36.400" y="0.000" width="10.800" height="30.000" fill="#d5dadf" />
<rect x="49.200" y="0.000" width="10.800" height="30.000" fill="#d5dadf" />
</svg>;
Icons.rfourforty = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="10.800" height="30.000" fill="#d5dadf" />
<rect x="12.800" y="0.000" width="10.800" height="30.000" fill="#d5dadf" />
<rect x="25.600" y="0.000" width="10.800" height="30.000" fill="#d5dadf" />
<rect x="38.400" y="0.000" width="21.600" height="30.000" fill="#d5dadf" />
</svg>;
Icons.fivecol = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="10.400" height="30.000" fill="#d5dadf" />
<rect x="12.400" y="0.000" width="10.400" height="30.000" fill="#d5dadf" />
<rect x="24.800" y="0.000" width="10.400" height="30.000" fill="#d5dadf" />
<rect x="37.200" y="0.000" width="10.400" height="30.000" fill="#d5dadf" />
<rect x="49.600" y="0.000" width="10.400" height="30.000" fill="#d5dadf" />
</svg>;
Icons.sixcol = <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="0.000" y="0.000" width="8.350" height="30.000" fill="#d5dadf" />
<rect x="10.330" y="0.000" width="8.350" height="30.000" fill="#d5dadf" />
<rect x="20.660" y="0.000" width="8.350" height="30.000" fill="#d5dadf" />
<rect x="30.990" y="0.000" width="8.350" height="30.000" fill="#d5dadf" />
<rect x="41.320" y="0.000" width="8.350" height="30.000" fill="#d5dadf" />
<rect x="51.650" y="0.000" width="8.350" height="30.000" fill="#d5dadf" />
</svg>;
Icons.aligntop = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<path d="M17.294,17.287l-14.588,0l0,-14.574l14.588,0c0,4.858 0,9.716 0,14.574Zm-13.738,-0.85l12.888,0l0,-12.874l-12.888,0c0,4.291 0,8.583 0,12.874Z" fillRule="nonzero" />
<rect x="4.489" y="4.545" width="11.022" height="2.512" />
</svg>;
Icons.alignmiddle = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<path d="M17.294,17.287l-14.588,0l0,-14.574l14.588,0c0,4.858 0,9.716 0,14.574Zm-13.738,-0.85l12.888,0l0,-12.874l-12.888,0c0,4.291 0,8.583 0,12.874Z" fillRule="nonzero" />
<rect x="4.489" y="8.744" width="11.022" height="2.512" />
</svg>;
Icons.alignbottom = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<path d="M17.294,17.287l-14.588,0l0,-14.574l14.588,0c0,4.858 0,9.716 0,14.574Zm-13.738,-0.85l12.888,0l0,-12.874l-12.888,0c0,4.291 0,8.583 0,12.874Z" fillRule="nonzero" />
<rect x="4.489" y="12.802" width="11.022" height="2.512" />
</svg>;
Icons.outlinetop = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="2.714" y="5.492" width="1.048" height="9.017" fill="#555d66" />
<rect x="16.265" y="5.498" width="1.023" height="9.003" fill="#555d66" />
<rect x="5.518" y="2.186" width="8.964" height="2.482" fill="#272b2f" />
<rect x="5.487" y="16.261" width="9.026" height="1.037" fill="#555d66" />
</svg>;
Icons.outlineright = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="2.714" y="5.492" width="1.046" height="9.017" fill="#555d66" />
<rect x="15.244" y="5.498" width="2.518" height="9.003" fill="#272b2f" />
<rect x="5.518" y="2.719" width="8.964" height="0.954" fill="#555d66" />
<rect x="5.487" y="16.308" width="9.026" height="0.99" fill="#555d66" />
</svg>;
Icons.outlinebottom = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="2.714" y="5.492" width="1" height="9.017" fill="#555d66" />
<rect x="16.261" y="5.498" width="1.027" height="9.003" fill="#555d66" />
<rect x="5.518" y="2.719" width="8.964" height="0.968" fill="#555d66" />
<rect x="5.487" y="15.28" width="9.026" height="2.499" fill="#272b2f" />
</svg>;
Icons.outlineleft = <svg width="20px" height="20px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fillRule="evenodd" clipRule="evenodd" strokeLinejoin="round" strokeMiterlimit="1.414">
<rect x="2.202" y="5.492" width="2.503" height="9.017" fill="#272b2f" />
<rect x="16.276" y="5.498" width="1.012" height="9.003" fill="#555d66" />
<rect x="5.518" y="2.719" width="8.964" height="0.966" fill="#555d66" />
<rect x="5.487" y="16.303" width="9.026" height="0.995" fill="#555d66" />
</svg>;
Icons.boxed = <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#CDCDCD"
  stroke="#9A9A9A"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path fill="#fff" d="M12.185 4.013H37.816V35.987H12.185z"></path>
<path
  fill="#CDCDCD"
  d="M14.224 6.006H35.783V14.636000000000001H14.224z"
></path>
<path
  fill="#CCC"
  fillRule="nonzero"
  d="M30.684 32.428H14.568c-.192 0-.351.353-.351.783 0 .429.159.783.351.783h16.116c.194 0 .352-.354.352-.783 0-.43-.158-.783-.352-.783zM35.324 29.284H14.677c-.246 0-.449.353-.449.783 0 .43.203.782.449.782h20.647c.246 0 .449-.352.449-.782 0-.43-.203-.783-.449-.783z"
></path>
<path
  fill="#CDCDCD"
  fillRule="nonzero"
  d="M35.324 26.14H14.677c-.246 0-.449.354-.449.784 0 .428.203.782.449.782h20.647c.246 0 .449-.354.449-.782 0-.43-.203-.784-.449-.784zM35.324 22.997H14.677c-.246 0-.449.353-.449.782 0 .43.203.784.449.784h20.647c.246 0 .449-.354.449-.784 0-.429-.203-.782-.449-.782z"
></path>
<path
  fill="#CCC"
  fillRule="nonzero"
  d="M35.324 19.853H14.677c-.246 0-.449.353-.449.783 0 .43.203.783.449.783h20.647c.246 0 .449-.353.449-.783 0-.43-.203-.783-.449-.783zM35.324 16.709H14.677c-.246 0-.449.354-.449.783 0 .429.203.783.449.783h20.647c.246 0 .449-.354.449-.783 0-.429-.203-.783-.449-.783z"
></path>
</svg>;
Icons.gridUnboxed =<svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#fff"
  stroke="#9A9A9A"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path fill="#CDCDCD" d="M4.555 4.68H22.976V11.581H4.555z"></path>
<path
  fill="#CCC"
  fillRule="nonzero"
  d="M18.62 25.807H4.849c-.164 0-.3.283-.3.627 0 .343.136.626.3.626H18.62c.165 0 .3-.283.3-.626 0-.344-.135-.627-.3-.627zM22.584 23.294H4.942c-.21 0-.383.282-.383.626 0 .343.173.625.383.625h17.642c.21 0 .383-.282.383-.625 0-.344-.173-.626-.383-.626z"
></path>
<path
  fill="#CDCDCD"
  fillRule="nonzero"
  d="M22.584 20.78H4.942c-.21 0-.383.282-.383.626 0 .343.173.626.383.626h17.642c.21 0 .383-.283.383-.626 0-.344-.173-.626-.383-.626zM22.584 18.266H4.942c-.21 0-.383.283-.383.626s.173.626.383.626h17.642c.21 0 .383-.283.383-.626s-.173-.626-.383-.626z"
></path>
<path
  fill="#CCC"
  fillRule="nonzero"
  d="M22.584 15.753H4.942c-.21 0-.383.282-.383.625 0 .344.173.626.383.626h17.642c.21 0 .383-.282.383-.626 0-.343-.173-.625-.383-.625zM22.584 13.238H4.942c-.21 0-.383.283-.383.626 0 .344.173.627.383.627h17.642c.21 0 .383-.283.383-.627 0-.343-.173-.626-.383-.626z"
></path>
<g>
  <path
	fill="#CDCDCD"
	d="M27.234 4.665H45.621V11.565999999999999H27.234z"
  ></path>
  <path
	fill="#CCC"
	fillRule="nonzero"
	d="M41.273 25.792H27.528c-.164 0-.3.283-.3.627 0 .343.136.626.3.626h13.745c.165 0 .299-.283.299-.626 0-.344-.134-.627-.299-.627zM45.23 23.279H27.621c-.21 0-.383.282-.383.626 0 .343.173.625.383.625H45.23c.209 0 .382-.282.382-.625 0-.344-.173-.626-.382-.626z"
  ></path>
  <path
	fill="#CDCDCD"
	fillRule="nonzero"
	d="M45.23 20.765H27.621c-.21 0-.383.283-.383.626s.173.626.383.626H45.23c.209 0 .382-.283.382-.626s-.173-.626-.382-.626zM45.23 18.251H27.621c-.21 0-.383.283-.383.626 0 .344.173.626.383.626H45.23c.209 0 .382-.282.382-.626 0-.343-.173-.626-.382-.626z"
  ></path>
  <path
	fill="#CCC"
	fillRule="nonzero"
	d="M45.23 15.738H27.621c-.21 0-.383.282-.383.625 0 .344.173.626.383.626H45.23c.209 0 .382-.282.382-.626 0-.343-.173-.625-.382-.625zM45.23 13.223H27.621c-.21 0-.383.283-.383.626 0 .344.173.627.383.627H45.23c.209 0 .382-.283.382-.627 0-.343-.173-.626-.382-.626z"
  ></path>
</g>
<g>
  <path fill="#CDCDCD" d="M4.555 30.385H22.976V37.286H4.555z"></path>
  <path fill="#CDCDCD" d="M27.234 30.37H45.621V37.271H27.234z"></path>
</g>
</svg>;
Icons.gridBoxed = <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinecap="round"
strokeLinejoin="round"
strokeMiterlimit="1.5"
clipRule="evenodd"
viewBox="0 0 50 40"
>
<path
  fill="#CDCDCD"
  stroke="#9A9A9A"
  strokeWidth="1"
  d="M49.007 2.918a1.9 1.9 0 00-1.898-1.898H2.891A1.9 1.9 0 00.993 2.918v34.164a1.9 1.9 0 001.898 1.898h44.218a1.9 1.9 0 001.898-1.898V2.918z"
></path>
<path fill="#fff" d="M4.415 4.606H23.453V28.356H4.415z"></path>
<path
  fill="#CDCDCD"
  d="M5.929 6.087H21.942999999999998V12.497H5.929z"
></path>
<path
  fill="#CCC"
  fillRule="nonzero"
  d="M18.156 25.712H6.185c-.143 0-.261.263-.261.582 0 .318.118.581.261.581h11.971c.143 0 .261-.263.261-.581 0-.319-.118-.582-.261-.582zM21.602 23.377H6.266c-.183 0-.334.262-.334.581 0 .32.151.582.334.582h15.336c.182 0 .333-.262.333-.582 0-.319-.151-.581-.333-.581z"
></path>
<path
  fill="#CDCDCD"
  fillRule="nonzero"
  d="M21.602 21.042H6.266c-.183 0-.334.263-.334.582 0 .318.151.581.334.581h15.336c.182 0 .333-.263.333-.581 0-.319-.151-.582-.333-.582zM21.602 18.707H6.266c-.183 0-.334.263-.334.581 0 .319.151.582.334.582h15.336c.182 0 .333-.263.333-.582 0-.318-.151-.581-.333-.581z"
></path>
<path
  fill="#CCC"
  fillRule="nonzero"
  d="M21.602 16.372H6.266c-.183 0-.334.262-.334.581 0 .32.151.582.334.582h15.336c.182 0 .333-.262.333-.582 0-.319-.151-.581-.333-.581zM21.602 14.037H6.266c-.183 0-.334.262-.334.581 0 .319.151.582.334.582h15.336c.182 0 .333-.263.333-.582 0-.319-.151-.581-.333-.581z"
></path>
<g>
  <path fill="#fff" d="M26.548 4.592H45.586V28.342H26.548z"></path>
  <path fill="#CDCDCD" d="M28.062 6.073H44.076V12.483H28.062z"></path>
  <path
	fill="#CCC"
	fillRule="nonzero"
	d="M40.289 25.698H28.318c-.143 0-.261.263-.261.582 0 .319.118.581.261.581h11.971c.143 0 .261-.262.261-.581 0-.319-.118-.582-.261-.582zM43.735 23.363H28.399c-.183 0-.333.262-.333.582 0 .319.15.581.333.581h15.336c.183 0 .333-.262.333-.581 0-.32-.15-.582-.333-.582z"
  ></path>
  <path
	fill="#CDCDCD"
	fillRule="nonzero"
	d="M43.735 21.028H28.399c-.183 0-.333.263-.333.582 0 .318.15.581.333.581h15.336c.183 0 .333-.263.333-.581 0-.319-.15-.582-.333-.582zM43.735 18.693H28.399c-.183 0-.333.263-.333.581 0 .32.15.582.333.582h15.336c.183 0 .333-.262.333-.582 0-.318-.15-.581-.333-.581z"
  ></path>
  <path
	fill="#CCC"
	fillRule="nonzero"
	d="M43.735 16.358H28.399c-.183 0-.333.262-.333.582 0 .319.15.581.333.581h15.336c.183 0 .333-.262.333-.581 0-.32-.15-.582-.333-.582zM43.735 14.023H28.399c-.183 0-.333.263-.333.581 0 .319.15.582.333.582h15.336c.183 0 .333-.263.333-.582 0-.318-.15-.581-.333-.581z"
  ></path>
</g>
<g>
  <path fill="#fff" d="M4.415 31.302H23.453V38.488H4.415z"></path>
  <path
	fill="#CDCDCD"
	d="M5.929 32.783H21.942999999999998V38.492000000000004H5.929z"
  ></path>
  <g>
	<path fill="#fff" d="M26.548 31.288H45.586V38.485H26.548z"></path>
	<path fill="#CDCDCD" d="M28.062 32.769H44.076V38.485H28.062z"></path>
  </g>
</g>
</svg>;
Icons.shoppingBag = <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
    >
      <path d="M19 5H5l1.5-2h11zm2.794.392L18.8 1.4A1 1 0 0018 1H6a1 1 0 00-.8.4L2.206 5.392A.978.978 0 002 6v14a2.997 2.997 0 003 3h14a2.997 2.997 0 003-3V6a.997.997 0 00-.189-.585L21.8 5.4zM4 7h16v13c0 .276-.111.525-.293.707S19.276 21 19 21H5c-.276 0-.525-.111-.707-.293S4 20.276 4 20zm11 3c0 .829-.335 1.577-.879 2.121S12.829 13 12 13s-1.577-.335-2.121-.879S9 10.829 9 10a1 1 0 00-2 0c0 1.38.561 2.632 1.464 3.536S10.62 15 12 15s2.632-.561 3.536-1.464S17 11.38 17 10a1 1 0 00-2 0z"></path>
    </svg>;
  Icons.shoppingCart = <svg
  xmlns="http://www.w3.org/2000/svg"
  width="24"
  height="24"
  viewBox="0 0 24 24"
>
  <path d="M11 21c0-.552-.225-1.053-.586-1.414a1.996 1.996 0 00-2.828 0 1.996 1.996 0 000 2.828 1.996 1.996 0 002.828 0c.361-.361.586-.862.586-1.414zm11 0c0-.552-.225-1.053-.586-1.414a1.996 1.996 0 00-2.828 0 1.996 1.996 0 000 2.828 1.996 1.996 0 002.828 0c.361-.361.586-.862.586-1.414zM7.221 7h14.57l-1.371 7.191A1 1 0 0119.4 15H9.666a1.016 1.016 0 01-.626-.203.99.99 0 01-.379-.603zM1 2h3.18l.848 4.239C5.136 6.676 5.53 7 6 7h1.221l-.4-2H6a1 1 0 00-.971 1.239L6.7 14.586A3.009 3.009 0 009.694 17H19.4a2.97 2.97 0 001.995-.727 3.02 3.02 0 00.985-1.683l1.602-8.402A1 1 0 0023 5H6.82L5.98.804A1 1 0 005 0H1a1 1 0 000 2z"></path>
</svg>;
Icons.sunAlt = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M8 4a4 4 0 100 8 4 4 0 000-8zm0 9a1 1 0 011 1v1a1 1 0 01-2 0v-1a1 1 0 011-1zM8 3a1 1 0 01-1-1V1a1 1 0 012 0v1a1 1 0 01-1 1zm7 4a1 1 0 010 2h-1a1 1 0 010-2h1zM3 8a1 1 0 01-1 1H1a1 1 0 010-2h1a1 1 0 011 1zm9.95 3.536l.707.707a1 1 0 01-1.414 1.414l-.707-.707a1 1 0 011.414-1.414zm-9.9-7.072l-.707-.707a.999.999 0 111.414-1.414l.707.707A.999.999 0 113.05 4.464zm9.9 0a.999.999 0 11-1.414-1.414l.707-.707a.999.999 0 111.414 1.414l-.707.707zm-9.9 7.072a1 1 0 011.414 1.414l-.707.707a1 1 0 01-1.414-1.414l.707-.707z"></path>
</svg>;
Icons.sunrise = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M18 18c0-1.657-.673-3.158-1.757-4.243S13.657 12 12 12s-3.158.673-4.243 1.757S6 16.343 6 18a1 1 0 002 0c0-1.105.447-2.103 1.172-2.828S10.895 14 12 14s2.103.447 2.828 1.172S16 16.895 16 18a1 1 0 002 0zM3.513 10.927l1.42 1.42a.999.999 0 101.414-1.414l-1.42-1.42a.999.999 0 10-1.414 1.414zM1 19h2a1 1 0 000-2H1a1 1 0 000 2zm20 0h2a1 1 0 000-2h-2a1 1 0 000 2zm-1.933-6.653l1.42-1.42a.999.999 0 10-1.414-1.414l-1.42 1.42a.999.999 0 101.414 1.414zM23 21H1a1 1 0 000 2h22a1 1 0 000-2zM8.707 6.707L11 4.414V9a1 1 0 002 0V4.414l2.293 2.293a.999.999 0 101.414-1.414l-4-4a1.006 1.006 0 00-1.414 0l-4 4a.999.999 0 101.414 1.414z"></path>
</svg>;
Icons.sunset = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M18 18c0-1.657-.673-3.158-1.757-4.243S13.657 12 12 12s-3.158.673-4.243 1.757S6 16.343 6 18a1 1 0 002 0c0-1.105.447-2.103 1.172-2.828S10.895 14 12 14s2.103.447 2.828 1.172S16 16.895 16 18a1 1 0 002 0zM3.513 10.927l1.42 1.42a.999.999 0 101.414-1.414l-1.42-1.42a.999.999 0 10-1.414 1.414zM1 19h2a1 1 0 000-2H1a1 1 0 000 2zm20 0h2a1 1 0 000-2h-2a1 1 0 000 2zm-1.933-6.653l1.42-1.42a.999.999 0 10-1.414-1.414l-1.42 1.42a.999.999 0 101.414 1.414zM23 21H1a1 1 0 000 2h22a1 1 0 000-2zM15.293 4.293L13 6.586V2a1 1 0 00-2 0v4.586L8.707 4.293a.999.999 0 10-1.414 1.414l4 4a.998.998 0 001.414 0l4-4a.999.999 0 10-1.414-1.414z"></path>
</svg>;
Icons.moonAlt = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M11.185 1.008A8.014 8.014 0 008.223 0 8.035 8.035 0 01.798 12.861a8.033 8.033 0 0013.328-.88 8.034 8.034 0 00-2.94-10.974z"></path>
</svg>;
Icons.sun = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M18 12c0-1.657-.673-3.158-1.757-4.243S13.657 6 12 6s-3.158.673-4.243 1.757S6 10.343 6 12s.673 3.158 1.757 4.243S10.343 18 12 18s3.158-.673 4.243-1.757S18 13.657 18 12zm-2 0c0 1.105-.447 2.103-1.172 2.828S13.105 16 12 16s-2.103-.447-2.828-1.172S8 13.105 8 12s.447-2.103 1.172-2.828S10.895 8 12 8s2.103.447 2.828 1.172S16 10.895 16 12zM11 1v2a1 1 0 002 0V1a1 1 0 00-2 0zm0 20v2a1 1 0 002 0v-2a1 1 0 00-2 0zM3.513 4.927l1.42 1.42a.999.999 0 101.414-1.414l-1.42-1.42a.999.999 0 10-1.414 1.414zm14.14 14.14l1.42 1.42a.999.999 0 101.414-1.414l-1.42-1.42a.999.999 0 10-1.414 1.414zM1 13h2a1 1 0 000-2H1a1 1 0 000 2zm20 0h2a1 1 0 000-2h-2a1 1 0 000 2zM4.927 20.487l1.42-1.42a.999.999 0 10-1.414-1.414l-1.42 1.42a.999.999 0 101.414 1.414zm14.14-14.14l1.42-1.42a.999.999 0 10-1.414-1.414l-1.42 1.42a.999.999 0 101.414 1.414z"></path>
</svg>;
Icons.moon = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M21.996 12.882a1 1 0 00-1.585-.9 6.11 6.11 0 01-3.188 1.162 5.948 5.948 0 01-3.95-1.158c-1.333-.985-2.139-2.415-2.367-3.935s.124-3.124 1.109-4.456a.998.998 0 00-.901-1.589 10.082 10.082 0 00-5.895 2.651 9.943 9.943 0 00-3.137 6.386c-.254 2.749.631 5.343 2.266 7.311s4.022 3.313 6.772 3.567 5.343-.631 7.311-2.266 3.313-4.022 3.567-6.772zm-2.429 1.792a7.988 7.988 0 01-2.416 3.441c-1.576 1.309-3.648 2.016-5.848 1.813s-4.108-1.278-5.417-2.854-2.016-3.648-1.813-5.848A7.932 7.932 0 016.58 6.12a8.058 8.058 0 012.731-1.672 8.008 8.008 0 002.772 9.146 7.94 7.94 0 005.272 1.545 8.083 8.083 0 002.21-.465z"></path>
</svg>;
Icons.checkbox = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M0 0v16h16V0H0zm15 15H1V1h14v14z"></path>
<path d="M2.5 8L4 6.5 6.5 9 12 3.5 13.5 5l-7 7z"></path>
</svg>;
Icons.checkbox_alt = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M14 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zM7 12.414L3.293 8.707l1.414-1.414L7 9.586l4.793-4.793 1.414 1.414L7 12.414z"></path>
</svg>;
Icons.check = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M14 2.5L5.5 11 2 7.5.5 9l5 5 10-10z"></path>
</svg>;
Icons.shield_check = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M13.739 3.061l-5.5-3a.497.497 0 00-.478 0l-5.5 3A.5.5 0 002 3.5v4c0 2.2.567 3.978 1.735 5.437.912 1.14 2.159 2.068 4.042 3.01a.502.502 0 00.448 0c1.883-.942 3.13-1.87 4.042-3.01 1.167-1.459 1.735-3.238 1.735-5.437v-4a.5.5 0 00-.261-.439zM6.5 11.296L3.704 8.5l.796-.795 2 2 5-5 .796.795-5.795 5.795z"></path>
</svg>;
Icons.disc = <svg
xmlns="http://www.w3.org/2000/svg"
width="16"
height="16"
viewBox="0 0 16 16"
>
<path d="M5 8a3 3 0 116 0 3 3 0 01-6 0z"></path>
</svg>;
Icons.arrowUp = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M5.707 12.707L11 7.414V19a1 1 0 002 0V7.414l5.293 5.293a.999.999 0 101.414-1.414l-7-7A1.008 1.008 0 0012 4a.997.997 0 00-.707.293l-7 7a.999.999 0 101.414 1.414z"></path>
</svg>;
Icons.arrowUp2 = <svg
xmlns="http://www.w3.org/2000/svg"
width="26"
height="28"
viewBox="0 0 26 28"
>
<path d="M25.172 15.172c0 .531-.219 1.031-.578 1.406l-1.172 1.172c-.375.375-.891.594-1.422.594s-1.047-.219-1.406-.594L16 13.172v11C16 25.297 15.062 26 14 26h-2c-1.062 0-2-.703-2-1.828v-11L5.406 17.75a1.96 1.96 0 01-2.812 0l-1.172-1.172c-.375-.375-.594-.875-.594-1.406s.219-1.047.594-1.422L11.594 3.578C11.953 3.203 12.469 3 13 3s1.047.203 1.422.578L24.594 13.75c.359.375.578.891.578 1.422z"></path>
</svg>;
Icons.chevronUp = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M18.707 14.293l-6-6a.999.999 0 00-1.414 0l-6 6a.999.999 0 101.414 1.414L12 10.414l5.293 5.293a.999.999 0 101.414-1.414z"></path>
</svg>;
Icons.chevronUp2 = <svg
xmlns="http://www.w3.org/2000/svg"
width="28"
height="28"
viewBox="0 0 28 28"
>
<path d="M26.297 20.797l-2.594 2.578a.99.99 0 01-1.406 0L14 15.078l-8.297 8.297a.99.99 0 01-1.406 0l-2.594-2.578a1.009 1.009 0 010-1.422L13.297 7.797a.99.99 0 011.406 0l11.594 11.578a1.009 1.009 0 010 1.422z"></path>
</svg>;
Icons.account = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M21 21v-2c0-1.38-.561-2.632-1.464-3.536S17.38 14 16 14H8c-1.38 0-2.632.561-3.536 1.464S3 17.62 3 19v2a1 1 0 002 0v-2c0-.829.335-1.577.879-2.121S7.171 16 8 16h8c.829 0 1.577.335 2.121.879S19 18.171 19 19v2a1 1 0 002 0zM17 7c0-1.38-.561-2.632-1.464-3.536S13.38 2 12 2s-2.632.561-3.536 1.464S7 5.62 7 7s.561 2.632 1.464 3.536S10.62 12 12 12s2.632-.561 3.536-1.464S17 8.38 17 7zm-2 0c0 .829-.335 1.577-.879 2.121S12.829 10 12 10s-1.577-.335-2.121-.879S9 7.829 9 7s.335-1.577.879-2.121S11.171 4 12 4s1.577.335 2.121.879S15 6.171 15 7z"></path>
</svg>;
Icons.account2 = <svg
xmlns="http://www.w3.org/2000/svg"
width="28"
height="28"
viewBox="0 0 28 28"
>
<path d="M23.797 20.922c-.406-2.922-1.594-5.516-4.25-5.875-1.375 1.5-3.359 2.453-5.547 2.453s-4.172-.953-5.547-2.453c-2.656.359-3.844 2.953-4.25 5.875C6.375 23.985 9.953 26 14 26s7.625-2.016 9.797-5.078zM20 10c0-3.313-2.688-6-6-6s-6 2.688-6 6 2.688 6 6 6 6-2.688 6-6zm8 4c0 7.703-6.25 14-14 14-7.734 0-14-6.281-14-14C0 6.266 6.266 0 14 0s14 6.266 14 14z"></path>
</svg>;
Icons.account3 = <svg
xmlns="http://www.w3.org/2000/svg"
width="28"
height="28"
viewBox="0 0 28 28"
>
<path d="M14 0c7.734 0 14 6.266 14 14 0 7.688-6.234 14-14 14-7.75 0-14-6.297-14-14C0 6.266 6.266 0 14 0zm9.672 21.109C25.125 19.109 26 16.656 26 14c0-6.609-5.391-12-12-12S2 7.391 2 14c0 2.656.875 5.109 2.328 7.109C4.89 18.312 6.25 16 9.109 16a6.979 6.979 0 009.782 0c2.859 0 4.219 2.312 4.781 5.109zM20 11c0-3.313-2.688-6-6-6s-6 2.688-6 6 2.688 6 6 6 6-2.688 6-6z"></path>
</svg>;
Icons.hours = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="28"
viewBox="0 0 24 28"
>
<path d="M14 8.5v7c0 .281-.219.5-.5.5h-5a.494.494 0 01-.5-.5v-1c0-.281.219-.5.5-.5H12V8.5c0-.281.219-.5.5-.5h1c.281 0 .5.219.5.5zm6.5 5.5c0-4.688-3.813-8.5-8.5-8.5S3.5 9.313 3.5 14s3.813 8.5 8.5 8.5 8.5-3.813 8.5-8.5zm3.5 0c0 6.625-5.375 12-12 12S0 20.625 0 14 5.375 2 12 2s12 5.375 12 12z"></path>
</svg>;
Icons.listFilter = <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
    >
      <path d="M8 7h13a1 1 0 000-2H8a1 1 0 000 2zm0 6h13a1 1 0 000-2H8a1 1 0 000 2zm0 6h13a1 1 0 000-2H8a1 1 0 000 2zM3 7a1 1 0 100-2 1 1 0 000 2zm0 6a1 1 0 100-2 1 1 0 000 2zm0 6a1 1 0 100-2 1 1 0 000 2z"></path>
    </svg>;
Icons.listFilterAlt = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M6 12.984v-1.969h12v1.969H6zM3 6h18v2.016H3V6zm6.984 12v-2.016h4.031V18H9.984z"></path>
</svg>;
Icons.close = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M5.293 6.707L10.586 12l-5.293 5.293a.999.999 0 101.414 1.414L12 13.414l5.293 5.293a.999.999 0 101.414-1.414L13.414 12l5.293-5.293a.999.999 0 10-1.414-1.414L12 10.586 6.707 5.293a.999.999 0 10-1.414 1.414z"></path>
</svg>;
Icons.closeAlt = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M5 2a2.997 2.997 0 00-3 3v14a2.997 2.997 0 003 3h14a2.997 2.997 0 003-3V5a2.997 2.997 0 00-3-3zm0 2h14c.276 0 .525.111.707.293S20 4.724 20 5v14c0 .276-.111.525-.293.707S19.276 20 19 20H5c-.276 0-.525-.111-.707-.293S4 19.276 4 19V5c0-.276.111-.525.293-.707S4.724 4 5 4zm9.293 4.293L12 10.586 9.707 8.293a.999.999 0 10-1.414 1.414L10.586 12l-2.293 2.293a.999.999 0 101.414 1.414L12 13.414l2.293 2.293a.999.999 0 101.414-1.414L13.414 12l2.293-2.293a.999.999 0 10-1.414-1.414z"></path>
</svg>;
Icons.closeAlt2 = <svg
xmlns="http://www.w3.org/2000/svg"
width="24"
height="24"
viewBox="0 0 24 24"
>
<path d="M23 12c0-3.037-1.232-5.789-3.222-7.778S15.037 1 12 1 6.211 2.232 4.222 4.222 1 8.963 1 12s1.232 5.789 3.222 7.778S8.963 23 12 23s5.789-1.232 7.778-3.222S23 15.037 23 12zm-2 0c0 2.486-1.006 4.734-2.636 6.364S14.486 21 12 21s-4.734-1.006-6.364-2.636S3 14.486 3 12s1.006-4.734 2.636-6.364S9.514 3 12 3s4.734 1.006 6.364 2.636S21 9.514 21 12zM8.293 9.707L10.586 12l-2.293 2.293a.999.999 0 101.414 1.414L12 13.414l2.293 2.293a.999.999 0 101.414-1.414L13.414 12l2.293-2.293a.999.999 0 10-1.414-1.414L12 10.586 9.707 8.293a.999.999 0 10-1.414 1.414z"></path>
</svg>;
Icons.logoArrow = <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 512 422"
>
<path d="M308.49 149.956L163.298 411.9H8.257L230.97 10.1l77.52 139.856zM236.216 323.65l48.916 88.25h-97.831l48.915-88.25zm10.445-18.843l98.861-178.356L503.743 411.9H306.022l-59.361-107.093z"></path>
</svg>;
Icons.logoCircle = <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 512 512"
>
<path d="M256 21c129.7 0 235 105.3 235 235S385.7 491 256 491 21 385.7 21 256 126.3 21 256 21zm0 302.025c18.213 0 33 14.775 33 32.975 0 18.2-14.787 32.975-33 32.975S223 374.2 223 356c0-18.2 14.787-32.975 33-32.975zm0-100c18.213 0 33 14.775 33 32.975 0 18.2-14.787 32.975-33 32.975S223 274.2 223 256c0-18.2 14.787-32.975 33-32.975zm0-100c18.213 0 33 14.775 33 32.975 0 18.2-14.787 32.975-33 32.975S223 174.2 223 156c0-18.2 14.787-32.975 33-32.975z"></path>
</svg>;
Icons.logoLine = <svg
xmlns="http://www.w3.org/2000/svg"
fillRule="evenodd"
strokeLinejoin="round"
strokeMiterlimit="2"
clipRule="evenodd"
viewBox="0 0 512 212"
>
<path d="M135.03 150.093l-56.671 46.61a5.974 5.974 0 01-7.627 0l-60.439-49.709c-2.029-1.668-2.812-4.473-1.953-6.996.858-2.522 3.17-4.21 5.766-4.21h2.369l15.299 12.582h-.229l43.001 35.366 43-35.366h-.171l15.298-12.582h4.772l15.298 12.582h-.228l43 35.366 43-35.366h-.17l15.298-12.582h4.772l15.298 12.582h-.228l43 35.366 43-35.366h-.171l15.298-12.582h4.772l15.299 12.582h-.229l43 35.366 43.001-35.366h-.171l15.298-12.582h2.312c2.596 0 4.908 1.688 5.766 4.21.859 2.523.076 5.328-1.953 6.996l-60.439 49.709a5.974 5.974 0 01-7.627 0l-56.671-46.61-56.671 46.61a5.975 5.975 0 01-7.628 0L256 150.093l-56.671 46.61a5.975 5.975 0 01-7.628 0l-56.671-46.61zm0-121.866l-56.671 46.61a5.974 5.974 0 01-7.627 0L10.293 25.128c-2.029-1.668-2.812-4.474-1.953-6.996.858-2.522 3.17-4.211 5.766-4.211h2.369l15.299 12.582h-.229l43.001 35.366 43-35.366h-.171l15.298-12.582h4.772l15.298 12.582h-.228l43 35.366 43-35.366h-.17l15.298-12.582h4.772l15.298 12.582h-.228l43 35.366 43-35.366h-.171l15.298-12.582h4.772l15.299 12.582h-.229l43 35.366 43.001-35.366h-.171l15.298-12.582h2.312c2.596 0 4.908 1.689 5.766 4.211.859 2.522.076 5.328-1.953 6.996l-60.439 49.709a5.974 5.974 0 01-7.627 0l-56.671-46.61-56.671 46.61a5.975 5.975 0 01-7.628 0L256 28.227l-56.671 46.61a5.975 5.975 0 01-7.628 0l-56.671-46.61z"></path>
</svg>;
export default Icons;
