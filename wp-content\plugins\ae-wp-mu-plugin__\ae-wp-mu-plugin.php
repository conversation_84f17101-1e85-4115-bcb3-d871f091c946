<?php
/**
 * Plugin Name: Americaneagle.com Managed Hosting
 * Plugin URI:  https://www.americaneagle.com/
 * Description: Americaneagle.com Managed Hosting Plugin to manage plugin and settings
 * Version:     1.0.4
 * Author:      Americaneagle.com
 * Author URI:  https://www.americaneagle.com/
 * Text Domain: americaneagle-wp-mu-plugin
 * Domain Path: /languages
 *
 * @package AmericaneagleWpMUPlugin
 */

namespace AmericaneagleWpMUPlugin;

// Useful global constants.
define( 'AMERICANEAGLE_WP_MU_PLUGIN_VERSION', '1.0.4' );
define( 'AMERICANEAGLE_WP_MU_PLUGIN_URL', \plugin_dir_url( __FILE__ ) );
define( 'AMERICANEAGLE_WP_MU_PLUGIN_PATH', \plugin_dir_path( __FILE__ ) );
define( 'AMERICANEAGLE_WP_MU_PLUGIN_INC', AMERICANEAGLE_WP_MU_PLUGIN_PATH . 'includes/' );

// Require Composer autoloader if it exists.
if ( file_exists( AMERICANEAGLE_WP_MU_PLUGIN_PATH . '/vendor/autoload.php' ) ) {
	require_once AMERICANEAGLE_WP_MU_PLUGIN_PATH . 'vendor/autoload.php';
}

// Initialise plugin.
( new Plugin() )->register();
