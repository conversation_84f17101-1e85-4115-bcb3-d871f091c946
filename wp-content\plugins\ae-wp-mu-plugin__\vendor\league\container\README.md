# Container (Dependency Injection)

[![Author](https://img.shields.io/badge/author-<PERSON>%20Bennett-blue?style=flat-square)](https://github.com/philipobenito)
[![Latest Version](https://img.shields.io/github/release/thephpleague/container.svg?style=flat-square)](https://github.com/thephpleague/container/releases)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE.md)
[![GitHub Actions Workflow Status](https://img.shields.io/github/actions/workflow/status/thephpleague/container/test.yml?style=flat-square)](https://github.com/thephpleague/container/actions/workflows/test.yml)
[![Coverage Status](https://img.shields.io/scrutinizer/coverage/g/thephpleague/container.svg?style=flat-square)](https://scrutinizer-ci.com/g/thephpleague/container/code-structure)
[![Quality Score](https://img.shields.io/scrutinizer/g/thephpleague/container.svg?style=flat-square)](https://scrutinizer-ci.com/g/thephpleague/container)
[![Total Downloads](https://img.shields.io/packagist/dt/league/container.svg?style=flat-square)](https://packagist.org/packages/league/container)

This package is compliant with [PSR-1], [PSR-2], [PSR-12], [PSR-4], [PSR-11] and [PSR-12]. If you notice compliance oversights, please send a patch via pull request.

[PSR-1]: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-1-basic-coding-standard.md
[PSR-2]: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-2-coding-style-guide.md
[PSR-12]: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-12-extended-coding-style-guide.md
[PSR-4]: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-4-autoloader.md
[PSR-11]: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-11-container.md
[PSR-12]: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-12-extended-coding-style-guide.md

## Install

Via Composer

``` bash
composer require league/container
```

## Requirements

The following versions of PHP are supported by this version.

* PHP 7.2
* PHP 7.3
* PHP 7.4
* PHP 8.0
* PHP 8.1
* PHP 8.2
* PHP 8.3
* PHP 8.4

## Documentation

Container has [full documentation](http://container.thephpleague.com), powered by [Jekyll](http://jekyllrb.com/).

Contribute to this documentation in the [docs/](https://github.com/thephpleague/container/tree/master/docs) sub-directory.

## Testing

Testing includes PHPUnit and PHPStan (Level 7).
``` bash
$ composer test
```

## Contributing

Please see [CONTRIBUTING](https://github.com/thephpleague/container/blob/master/CONTRIBUTING.md) for details.

## Security

If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.

## Credits

- [Phil Bennett](https://github.com/philipobenito)
- [All Contributors](https://github.com/thephpleague/container/contributors)
- `Orno\Di` contributors

## License

The MIT License (MIT). Please see [License File](https://github.com/thephpleague/container/blob/master/LICENSE.md) for more information.
