<?php
/**
 * American Eagle WP MU Plugin Plugins deletion Class
 *
 * @package AmericaneagleWpMUPlugin\Admin
 */

namespace AmericaneagleWpMUPlugin\Admin;

use AmericaneagleWpMUPlugin\BaseAbstract;
use AmericaneagleWpMUPlugin\is_ae_user;

/**
 * WordPress Plugins deletion Functionality.
 */
class Plugins extends BaseAbstract {

	/**
	 * Array of blacklisted plugins.
	 *
	 * @var array
	 */
	private array $plugins_notice;

	/**
	 * Blocks blacklisted plugins from being activated.
	 *
	 * @param string $plugin       Plugin.
	 * @param array  $network_wide Netowrk wide.
	 *
	 * @return void
	 */
	public function block_blacklisted_plugins( $plugin, $network_wide ): void {
		$plugins = [];

		foreach( self::BLACKLISTED_PLUGINS as $key => $blocked_plugin ) {
			if ( $plugin === $blocked_plugin ) {
				$plugins   = get_transient( 'blocked-plugin' ) ?: [];
				$plugins[] = $key;
				set_transient( 'blocked-plugin', $plugins );
				delete_plugins( [ $blocked_plugin ] );

				$redirect = self_admin_url( 'plugins.php' );
				wp_redirect( $redirect );
				exit;
			}
		}
	}

	/**
	 * Displays an admin notice if a plugin has been blocked and an attempt is made to activate it.
	 *
	 * @return void
	 */
	public function blocked_plugin_activation_notice(): void {
		$plugins = get_transient( 'blocked-plugin' ) ?? [];

		if ( ! empty( $plugins ) ) {
			wp_admin_notice(
				__( implode( ", ", $plugins ) . ' has been deleted due to being blacklisted.', 'americaneagle-wp-mu-plugin' ),
				array(
					'id'                 => 'warning',
					'additional_classes' => [ 'updated' ],
					'dismissible'        => false,
				)
			);

			delete_transient( 'blocked-plugin' );
		}
	}

	/**
	 * Hides plugins.
	 *
	 * @param array $plugins Plugins list.
	 *
	 * @return array
	 */
	public function hide_plugins( array $plugins ): array {
		if ( is_ae_user() ) {
			return $plugins;
		}

		// Hide AE Plguin.
		foreach( self::AE_PLUGINS as $plugin ) {
			unset( $plugins[ $plugin ] );
		}

		return $plugins;
	}

	/**
	 * Hides MU plugins.
	 *
	 * @param array $plugins Plugins list.
	 *
	 * @return array
	 */
	public function hide_mu_plugins( array $views ): array {
		if ( is_ae_user() ) {
			return $views;
		}

		if ( isset( $views['mustuse'] ) ) {
			unset( $views['mustuse'] );
		}

		return $views;
	}

	/**
	 * Display dashboard widget for blacklisted plugins.
	 *
	 * @return void
	 */
	public function blacklisted_plugins_notice(): void {
		global $wp_meta_boxes;

		$current_plugins = get_plugins();

		// Admin notice.
		foreach( self::BLACKLISTED_PLUGINS as $key => $blocked_plugin ) {
			if ( isset( $current_plugins[ $blocked_plugin ] ) ) {
				$this->plugins_notice[] = $key;
			}
		}

		// Build dashboard widget based on if there are any blacklised plugins.
		if ( ! empty( $this->plugins_notice ) ) {
			wp_add_dashboard_widget(
				'blacklisted_plugins',
				'Blacklisted Plugins',
				[ $this, 'blacklisted_plugins' ]
			);

			$dashboard = $wp_meta_boxes['dashboard']['normal']['core'];
			$widget    = [
				'blacklisted_plugins' => $dashboard['blacklisted_plugins'],
			];
		
			unset( $dashboard['blacklisted_plugins'] );

			$sorted_dashboard                             = array_merge( $widget, $dashboard );
			$wp_meta_boxes['dashboard']['normal']['core'] = $sorted_dashboard;
		}
	}

	/**
	 * Output dashboard message.
	 *
	 * @return void
	 */
	public function blacklisted_plugins(): void {
	?>
		<p><?php _e( 'The following plugins need to be removed as they represent a security risk to the site.', 'americaneagle-wp-mu-plugin' ) ?></p>
		<p><strong><?php _e( 'If they are not replaced they will be deleted.', 'americaneagle-wp-mu-plugin' ) ?></strong></p>
		<ul>
		<?php
			foreach( $this->plugins_notice as $plugin ) {
				?>
				<li><?php echo esc_attr( $plugin ); ?>
				</li>
				<?php
			}
		?>
		</ul>
	<?php
	}
}
